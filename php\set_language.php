<?php
session_start();

// Verfügbare Sprachen
$available_languages = ['de', 'en', 'ar', 'tr'];

// Sprache aus URL-Parameter lesen
if (isset($_GET['lang']) && in_array($_GET['lang'], $available_languages)) {
    $lang = $_GET['lang'];
    
    // Sprache in Session speichern
    $_SESSION['user_language'] = $lang;
    
    // Auch als Cookie setzen
    setcookie('user_language', $lang, time() + (86400 * 30), "/"); // 30 Tage gültig
    
    // Erfolg zurückmelden
    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'language' => $lang]);
} else {
    // Fehler zurückmelden
    header('Content-Type: application/json');
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid language']);
}
?>