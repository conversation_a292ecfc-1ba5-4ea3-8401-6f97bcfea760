.bg-primary { background-color: #013476; }
.bg-secondary { background-color: #1CC5CC; }
.text-primary { color: #013476; }
.text-secondary { color: #1CC5CC; }
.border-primary { border-color: #013476; }
.border-secondary { border-color: #1CC5CC; }
.tab-container { display: flex; border-bottom: 1px solid #e5e7eb; margin-bottom: 1.5rem; }
.tab { padding: 0.75rem 1.5rem; cursor: pointer; font-weight: 500; border-bottom: 3px solid transparent; transition: all 0.3s ease; }
.tab.active { border-bottom-color: #1CC5CC; color: #013476; }
.tab-content { display: none; }
.tab-content.active { display: block; }
.input-error { border-color: #f56565; }
.status-pending { background-color: #FEF3C7; color: #92400E; }
.status-approved { background-color: #D1FAE5; color: #065F46; }
.status-rejected { background-color: #FEE2E2; color: #92400E; }
.table-responsive { overflow-x: auto; }