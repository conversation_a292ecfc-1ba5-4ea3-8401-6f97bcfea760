<?php
require_once __DIR__ . '/config.php'; // Stellt sicher, dass der Pfad korrekt ist

$dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION, // Fehler als Exceptions werfen
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,       // Ergebnisse als assoziatives Array
    PDO::ATTR_EMULATE_PREPARES   => false,                  // Echte Prepared Statements nutzen
];

try {
     $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
     // echo "Datenbankverbindung erfolgreich!"; // Nur zum Testen
} catch (\PDOException $e) {
     // In einer Produktionsumgebung sollte hier ein allgemeiner Fehler geloggt
     // und dem Benutzer eine generische Fehlermeldung angezeigt werden.
     // Nicht die $e->getMessage() direkt ausgeben!
     error_log("Datenbankverbindungsfehler: " . $e->getMessage()); // Fehler loggen
     // header('Content-Type: application/json'); // Sicherstellen, dass JSON gesendet wird
     // echo json_encode(['success' => false, 'error_code' => 'db_conn_failed', 'message' => 'Database connection error.']);
     // exit; // Skriptausführung beenden

     // Für die Entwicklung kann die Fehlermeldung hilfreich sein:
     throw new \PDOException($e->getMessage(), (int)$e->getCode());
}

// Die Variable $pdo steht nun für die Datenbankverbindung zur Verfügung
?>