function setupMobileMenu() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('active');
            // Optional: Ändere das Icon (Bars zu X und zurück)
            const icon = mobileMenuButton.querySelector('i');
            if (icon) {
                icon.classList.toggle('fa-bars');
                icon.classList.toggle('fa-times'); // FontAwesome Klasse für 'X'
            }
        });

        // Schließe Menü, wenn auf einen Link darin geklickt wird (optional)
        mobileMenu.querySelectorAll('a').forEach(link => {
            link.addEventListener('click', () => {
                mobileMenu.classList.remove('active');
                // Icon zurücksetzen
                const icon = mobileMenuButton.querySelector('i');
                 if (icon) {
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-bars');
                }
            });
        });

    } else {
        // console.info('Mobile menu button or menu container not found.'); // Kein Fehler, nur Info
    }
}