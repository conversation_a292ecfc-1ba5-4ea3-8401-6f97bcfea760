<?php
header('Content-Type: application/json');

require_once __DIR__ . '/config.php'; // Config laden
require_once BASE_PATH . '/php/db_connect.php'; // DB Connect mit BASE_PATH
require_once BASE_PATH . '/php/recaptcha_verify.php'; // reCAPTCHA mit BASE_PATH

$response = ['success' => false];

// Nur POST erlauben (oder wie auch immer das Payment Gateway Daten sendet!)
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['error_code'] = 'invalid_method';
    echo json_encode($response);
    exit;
}

// *** WICHTIG: Die Verarbeitung hier hängt STARK vom Payment Gateway ab! ***
// 1. Normalerweise erhältst du einen Callback vom Gateway (z.B. PayPal IPN, Stripe Webhook)
//    oder der Nutzer wird nach erfolgreicher Zahlung zurückgeleitet MIT Zahlungsdetails.
// 2. Du musst diese Daten sicher validieren (Signatur prüfen etc.).
// 3. Erst DANN die Daten in 'spender' und 'spenden' speichern.

// Beispielhafte Logik, wenn Daten DIREKT vom Frontend kämen (NICHT SICHER für echte Zahlungen!)
// Dies dient nur als Platzhalter für die DB-Interaktion nach erfolgreicher Zahlung!

$recaptchaToken = $_POST['g-recaptcha-response'] ?? '';
if (!verifyRecaptcha($recaptchaToken)) {
    $response['error_code'] = 'recaptcha_failed';
    echo json_encode($response);
    exit;
}

// Daten holen (Beispiel)
$vorname = trim($_POST['firstname'] ?? '');
$nachname = trim($_POST['lastname'] ?? '');
$email = filter_var(trim($_POST['email'] ?? ''), FILTER_SANITIZE_EMAIL);
$betrag = filter_var(trim($_POST['amount'] ?? ''), FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
$strasse = trim($_POST['street'] ?? '');
$plz = trim($_POST['plz'] ?? '');
$ort = trim($_POST['ort'] ?? '');
$land = trim($_POST['land'] ?? '');

// Validierung (Beispiel)
if (empty($vorname) || empty($nachname) || empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL) || empty($betrag) || $betrag <= 0 || empty($strasse) || empty($plz) || empty($ort) || empty($land)) {
     $response['error_code'] = 'validation_failed';
     $response['message'] = 'Ungültige Eingabe für Spende.';
     echo json_encode($response);
     exit;
}

// Hier müsste die Logik folgen:
// 1. Spender suchen oder neu anlegen
// 2. Spende eintragen

// --- Platzhalter für DB-Operationen ---
// try {
//   // Begin Transaction
//   $pdo->beginTransaction();
//
//   // 1. Spender suchen/anlegen
//   $stmtSpender = $pdo->prepare("SELECT spender_id FROM spender WHERE email = :email");
//   $stmtSpender->execute(['email' => $email]);
//   $spender_id = $stmtSpender->fetchColumn();
//
//   if (!$spender_id) {
//      $stmtInsertSpender = $pdo->prepare("INSERT INTO spender (vorname, nachname, email, strasse_hausnummer, plz, ort, land) VALUES (?, ?, ?, ?, ?, ?, ?)");
//      $stmtInsertSpender->execute([$vorname, $nachname, $email, $strasse, $plz, $ort, $land]);
//      $spender_id = $pdo->lastInsertId();
//   } else {
//       // Optional: Spenderdaten aktualisieren
//       // $stmtUpdateSpender = $pdo->prepare("UPDATE spender SET vorname=?, nachname=?, strasse_hausnummer=?, plz=?, ort=?, land=? WHERE spender_id=?");
//       // $stmtUpdateSpender->execute([...]);
//   }
//
//   // 2. Spende eintragen
//   $stmtSpende = $pdo->prepare("INSERT INTO spenden (spender_id, betrag, spenden_datum, zahlungsart, transaktions_id) VALUES (?, ?, NOW(), ?, ?)");
//   // Zahlungsart und Transaktions-ID müssten vom Gateway kommen!
//   $stmtSpende->execute([$spender_id, $betrag, 'BEISPIEL_PAYMENT_METHOD', 'BEISPIEL_TRANSACTION_ID']);
//
//   // Commit Transaction
//   $pdo->commit();
//   $response['success'] = true;
//
// } catch (PDOException $e) {
//   $pdo->rollBack(); // Bei Fehler zurückrollen
//   error_log("Donation DB Error: " . $e->getMessage());
//   $response['error_code'] = 'db_error';
//   $response['message'] = 'Fehler beim Speichern der Spende.';
// }

// --- Ende Platzhalter ---

// Für den Moment geben wir einfach Erfolg zurück (NUR FÜR FRONTEND-TESTS!)
$response['success'] = true;
$response['message'] = '(Simulierter Erfolg - Backend-Logik für Spenden fehlt noch!)';


echo json_encode($response);
?>