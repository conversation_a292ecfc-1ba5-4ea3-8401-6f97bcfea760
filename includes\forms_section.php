<?php
// includes/forms_section.php

if (!defined('RECAPTCHA_SITE_KEY')) {
    $configPath = __DIR__ . '/../php/config.php';
    if (file_exists($configPath)) {
        require_once $configPath;
    }
}
if (!defined('RECAPTCHA_SITE_KEY')) {
    define('RECAPTCHA_SITE_KEY', 'YOUR_RECAPTCHA_SITE_KEY_GOES_HERE');
    error_log("WARNUNG: RECAPTCHA_SITE_KEY nicht in config.php gefunden. Verwende Platzhalter in forms_section.php.");
}

?>
<!-- Membership & Donation Section -->
<section id="mitglied-werden" class="py-16 bg-gray-100 scroll-mt-24">
     <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 id="form-section-title" class="text-3xl font-bold text-primary mb-4" data-translate="join_title">Mitglied werden</h2>
            <div class="w-24 h-1 bg-secondary mx-auto mb-6"></div>
            <p class="text-gray-600 max-w-3xl mx-auto" data-translate="join_subtitle">Werden Sie Teil unserer Bewegung für ein besseres Lünen oder unterstützen Sie uns mit einer Spende.</p>
        </div>

        <div class="max-w-3xl mx-auto bg-white rounded-lg shadow-md p-8">
            <!-- Tabs Navigation -->
            <div class="tab-container">
                <div class="tab active" data-tab="membership" data-translate="membership_tab">Mitgliedschaft</div>
                <div class="tab" data-tab="donation" data-translate="donation_tab">Spenden</div> 
            </div>

            <!-- ====================== -->
            <!-- == Membership Form == -->
            <!-- ====================== -->
            <div id="membership-tab" class="tab-content active">
                <form id="membership-form" method="POST" action="/php/submit_membership.php" class="space-y-6">
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label for="anrede" class="block text-gray-700 font-medium mb-2" data-translate="salutation">Anrede*</label>
                            <select id="anrede" name="anrede" required class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                                <option value="Herr" data-translate="mr">Herr</option>
                                <option value="Frau" data-translate="mrs">Frau</option>
                            </select>
                        </div>
                        <div>
                            <label for="titel" class="block text-gray-700 font-medium mb-2" data-translate="titel">Titel</label>
                            <input type="text" id="titel" name="titel" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        </div>
                    </div>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label for="vorname" class="block text-gray-700 font-medium mb-2" data-translate="first_name">Vorname*</label>
                            <input type="text" id="vorname" name="vorname" required class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        </div>
                        <div>
                            <label for="nachname" class="block text-gray-700 font-medium mb-2" data-translate="last_name">Nachname*</label>
                            <input type="text" id="nachname" name="nachname" required class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        </div>
                    </div>
                    <div>
                        <label for="organisation" class="block text-gray-700 font-medium mb-2" data-translate="organization">Unternehmen/Verein/Organisation (bei juristischen Personen)</label>
                        <input type="text" id="organisation" name="organisation" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                    </div>
                    <div>
                        <label for="strasse_hausnummer" class="block text-gray-700 font-medium mb-2" data-translate="street">Straße und Hausnummer*</label>
                        <input type="text" id="strasse_hausnummer" name="strasse_hausnummer" required class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                    </div>
                    <div class="grid md:grid-cols-3 gap-6">
                        <div>
                            <label for="plz" class="block text-gray-700 font-medium mb-2" data-translate="zip">PLZ*</label>
                            <input type="text" id="plz" name="plz" required pattern="[0-9]{5}" title="Bitte geben Sie eine 5-stellige PLZ ein." class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        </div>
                        <div>
                            <label for="ort" class="block text-gray-700 font-medium mb-2" data-translate="city">Ort*</label>
                            <input type="text" id="ort" name="ort" required class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        </div>
                        <div>
                            <label for="land" class="block text-gray-700 font-medium mb-2" data-translate="country">Land*</label>
                            <select id="land" name="land" required class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                                <option value="Deutschland" data-translate="germany">Deutschland</option>
                                <option value="Österreich" data-translate="austria">Österreich</option>
                                <option value="Schweiz" data-translate="switzerland">Schweiz</option>
                                <option value="Andere" data-translate="other">Andere</option>
                            </select>
                        </div>
                    </div>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label for="geburtsdatum" class="block text-gray-700 font-medium mb-2" data-translate="birthdate">Geburtsdatum*</label>
                            <input type="date" id="geburtsdatum" name="geburtsdatum" required max="<?php echo date('Y-m-d'); ?>" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        </div>
                        <div>
                            <label for="telefon" class="block text-gray-700 font-medium mb-2" data-translate="phone_number">Telefon*</label>
                            <input type="tel" id="telefon" name="telefon" required class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        </div>
                    </div>
                    <div>
                        <label for="email" class="block text-gray-700 font-medium mb-2" data-translate="email_address">E-Mail-Adresse*</label>
                        <input type="email" id="email" name="email" required class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                    </div>

                    <!-- Beitrag und Zahlungsintervall -->
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label for="beitrag" class="block text-gray-700 font-medium mb-2" data-translate="contribution_amount_optional">Beitrag (€)</label>
                            <!-- required entfernt -->
                            <input type="number" id="beitrag" name="beitrag" min="2.50" step="0.01" placeholder="Standard: 2.50" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent" data-translate-placeholder="contribution_placeholder_optional">
                            <p class="text-xs text-gray-500 mt-1" data-translate="contribution_info_transfer">Mindestbeitrag: 2,50 €. Der monatliche Beitrag ist je nach gewähltem Zahlungsintervall eigenständig zu überweisen.</p>
                        </div>
                        <div>
                            <label for="gewuenschtes_zahlungsintervall" class="block text-gray-700 font-medium mb-2" data-translate="payment_interval">Zahlungsintervall*</label>
                            <select id="gewuenschtes_zahlungsintervall" name="gewuenschtes_zahlungsintervall" required class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                                <option value="monatlich" data-translate="monthly">Monatlich</option>
                                <option value="vierteljährlich" data-translate="quarterly">Vierteljährlich</option>
                                <option value="halbjährlich" data-translate="semiannually">Halbjährlich</option>
                                <option value="jährlich" data-translate="annually">Jährlich</option>
                            </select>
                        </div>
                    </div>

                    <!-- Checkboxen -->
                    <div class="space-y-4 pt-4">
                        <label class="flex items-start">
                            <input type="checkbox" id="student" name="ist_student" value="1" class="form-checkbox h-5 w-5 text-secondary rounded mt-1 border-gray-300 focus:ring-secondary">
                            <span class="ml-2 text-gray-700" data-translate="student_checkbox">Ich bin ein Student</span>
                        </label>
                        <label class="flex items-start">
                            <input type="checkbox" id="bestaetigung_richtigkeit" name="bestaetigung_richtigkeit" required class="form-checkbox h-5 w-5 text-secondary rounded mt-1 border-gray-300 focus:ring-secondary">
                            <span class="ml-2 text-gray-700" data-translate="accuracy_checkbox">Ich bestätige die Richtigkeit meiner Angaben*</span>
                        </label>
                        <label class="flex items-start">
                            <input type="checkbox" id="datenschutz_akzeptiert" name="datenschutz_akzeptiert" required class="form-checkbox h-5 w-5 text-secondary rounded mt-1 border-gray-300 focus:ring-secondary">
                            <span class="ml-2 text-gray-700">
                                <span data-translate="privacy_text1">Ich habe die</span>
                                <a href="#datenschutz" class="text-secondary hover:underline mx-1 dynamic-content-link" data-target="privacy-policy-content" data-translate="privacy_policy_link_text">Datenschutzerklärung</a>
                                <span data-translate="privacy_text2">gelesen und stimme ihr zu*</span>
                            </span>
                        </label>
                         <p id="membership-privacy-error" class="text-red-500 text-xs mt-1 ml-7 hidden" data-translate="privacy_error">Bitte stimmen Sie der Datenschutzerklärung zu.</p>

                        <label class="flex items-start">
                            <input type="checkbox" id="keine_andere_partei" name="keine_andere_partei" required class="form-checkbox h-5 w-5 text-secondary rounded mt-1 border-gray-300 focus:ring-secondary">
                            <span class="ml-2 text-gray-700" data-translate="no_party_checkbox">
                                Ich versichere, dass ich in keiner anderen Partei/Wählergemeinschaft engagiert bin*
                            </span>
                        </label>
                    </div>

                    <!-- reCAPTCHA -->
                    <div class="g-recaptcha mt-4 mb-4" data-sitekey="<?php echo htmlspecialchars(RECAPTCHA_SITE_KEY); ?>"></div>
                    <p id="membership-recaptcha-error" class="text-red-500 text-xs mt-1 hidden" data-translate="recaptcha_error">Bitte bestätigen Sie, dass Sie kein Roboter sind.</p>

                    <!-- Submit Button -->
                    <div class="pt-4">
                        <button type="submit" class="bg-secondary hover:bg-primary text-white px-8 py-3 rounded-full font-bold transition duration-300 w-full md:w-auto" data-translate="apply_membership">
                            Mitgliedschaft beantragen
                        </button>
                    </div>
                </form>
            </div> <!-- Ende Membership Form Tab -->

            <!-- Donation Form -->
            <div id="donation-tab" class="tab-content">
                <form id="donation-form" class="space-y-6">
                     <div class="grid md:grid-cols-2 gap-6">
                        <div><label for="donation-firstname" class="block text-gray-700 font-medium mb-2" data-translate="first_name">Vorname*</label><input type="text" id="donation-firstname" name="vorname" required class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent"></div>
                        <div><label for="donation-lastname" class="block text-gray-700 font-medium mb-2" data-translate="last_name">Nachname*</label><input type="text" id="donation-lastname" name="name" required class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent"></div>
                     </div>
                     
                     <div>
                        <label for="donation-organisation" class="block text-gray-700 font-medium mb-2" data-translate="organization">Unternehmen/Verein/Organisation (bei juristischen Personen)</label>
                        <input type="text" id="donation-organisation" name="zusatzfeld_organisation" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                     </div>
                     
                     <div>
                        <label for="donation-email" class="block text-gray-700 font-medium mb-2" data-translate="email_address">E-Mail-Adresse*</label>
                        <input type="email" id="donation-email" name="email" required class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                     </div>
                     
                     <div>
                        <label for="donation-telefon" class="block text-gray-700 font-medium mb-2" data-translate="phone_number">Telefon*</label>
                        <input type="tel" id="donation-telefon" name="telefonprivat" required class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                     </div>
                     
                     <div>
                        <label for="donation-geburtsdatum" class="block text-gray-700 font-medium mb-2" data-translate="birthdate">Geburtsdatum*</label>
                        <input type="date" id="donation-geburtsdatum" name="geburtsdatum" required max="<?php echo date('Y-m-d'); ?>" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                     </div>
                     
                     <div>
                        <label for="donation-strasse" class="block text-gray-700 font-medium mb-2" data-translate="street">Straße und Hausnummer*</label>
                        <input type="text" id="donation-strasse" name="strasse" required class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                     </div>
                     
                     <div class="grid md:grid-cols-3 gap-6">
                        <div>
                            <label for="donation-plz" class="block text-gray-700 font-medium mb-2" data-translate="zip">PLZ*</label>
                            <input type="text" id="donation-plz" name="plz" required pattern="[0-9]{5}" title="Bitte geben Sie eine 5-stellige PLZ ein." class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        </div>
                        <div>
                            <label for="donation-ort" class="block text-gray-700 font-medium mb-2" data-translate="city">Ort*</label>
                            <input type="text" id="donation-ort" name="ort" required class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        </div>
                        <div>
                            <label for="donation-land" class="block text-gray-700 font-medium mb-2" data-translate="country">Land*</label>
                            <select id="donation-land" name="staat" required class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                                <option value="Deutschland" data-translate="germany">Deutschland</option>
                                <option value="Österreich" data-translate="austria">Österreich</option>
                                <option value="Schweiz" data-translate="switzerland">Schweiz</option>
                                <option value="Andere" data-translate="other">Andere</option>
                            </select>
                        </div>
                     </div>
                     
                     <div>
                        <label for="donation-amount" class="block text-gray-700 font-medium mb-2" data-translate="donation_amount">Spendenbetrag*</label>
                        <div class="relative rounded-md shadow-sm">
                            <input type="number" id="donation-amount" name="individuellerbeitrag" min="1" step="any" placeholder="Betrag in €" required class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent" data-translate-placeholder="donation_amount_placeholder">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"><span class="text-gray-500">€</span></div>
                        </div>
                     </div>
                     
                     <!-- Mitgliedschaftsfrage -->
                     <div class="pt-4">
                        <label class="block text-gray-700 font-medium mb-2" data-translate="is_member">Bist du Mitglied?*</label>
                        <div class="flex space-x-6">
                            <label class="flex items-center">
                                <input type="radio" id="donation-not-member" name="bereitsmitglied" value="0" checked class="form-radio h-5 w-5 text-secondary border-gray-300 focus:ring-secondary">
                                <span class="ml-2 text-gray-700" data-translate="no">Nein</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" id="donation-is-member" name="bereitsmitglied" value="1" class="form-radio h-5 w-5 text-secondary border-gray-300 focus:ring-secondary">
                                <span class="ml-2 text-gray-700" data-translate="yes">Ja</span>
                            </label>
                        </div>
                     </div>
                     
                     <div class="pt-4">
                        <label class="flex items-start">
                            <input type="checkbox" id="donation-bestaetigung-richtigkeit" name="zusatzfeld_richtigkeit" required class="form-checkbox h-5 w-5 text-secondary rounded mt-1 border-gray-300 focus:ring-secondary">
                            <span class="ml-2 text-gray-700" data-translate="accuracy_checkbox">Ich bestätige die Richtigkeit meiner Angaben*</span>
                        </label>
                     </div>
                     
                     <div class="pt-4">
                        <label class="flex items-start">
                            <input type="checkbox" id="donation-privacy-agree" name="zusatzfeld_datenschutz" required class="form-checkbox h-5 w-5 text-secondary rounded mt-1 border-gray-300 focus:ring-secondary">
                            <span class="ml-2 text-gray-700">
                                <span data-translate="privacy_text1">Ich habe die</span>
                                <a href="#datenschutz" class="text-secondary hover:underline mx-1 dynamic-content-link" data-target="privacy-policy-content" data-translate="privacy_policy_link_text">Datenschutzerklärung</a>
                                <span data-translate="privacy_text2">gelesen und stimme ihr zu*</span>
                            </span>
                        </label>
                        <p id="donation-privacy-error" class="text-red-500 text-xs mt-1 ml-7 hidden" data-translate="privacy_error">Bitte stimmen Sie der Datenschutzerklärung zu.</p>
                     </div>
                     
                     <div class="g-recaptcha mt-4 mb-4" data-sitekey="<?php echo htmlspecialchars(RECAPTCHA_SITE_KEY); ?>" data-callback="recaptchaCallback" data-error-callback="recaptchaErrorCallback"></div>
                     <p id="donation-recaptcha-error" class="text-red-500 text-xs mt-1 hidden" data-translate="recaptcha_error">Bitte bestätigen Sie, dass Sie kein Roboter sind.</p>
                     
                     <div class="pt-6">
                        <div class="flex flex-col space-y-4">
                            <button type="button" id="paypal-button" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded font-bold transition duration-300 flex items-center justify-center">
                                <i class="fab fa-paypal mr-2"></i><span data-translate="paypal_donate">Mit PayPal spenden</span>
                            </button>
                            <!-- <button type="button" id="sofort-button" class="bg-secondary hover:bg-primary text-white px-6 py-3 rounded font-bold transition duration-300 flex items-center justify-center">
                                <i class="fas fa-university mr-2"></i><span data-translate="bank_transfer">Sofortüberweisung</span>
                            </button> -->
                        </div>
                     </div>
                </form>
            </div>

        </div>
    </div>
</section>
