
// Instagram Feed Integration

// Globale Variablen für den Zugriff außerhalb des DOMContentLoaded-Handlers
let postsContainer;
let instagramPosts = [];
let currentPostIndex = 0;
let modal, modalContent, modalImage, modalCaption, modalText, modalDate, modalLink;
let closeModalButton, prevPostButton, nextPostButton, prevPostMobileButton, nextPostMobileButton, showMoreTextButton;

document.addEventListener('DOMContentLoaded', function() {
    // Konfiguration
    const INSTAGRAM_USERNAME = 'vision4luenen'; // Ihr Instagram-Benutzername
    const POST_COUNT = 6; // Anzahl der anzuzeigenden Beiträge
    
    // Elemente
    postsContainer = document.getElementById('instagram-posts');
    modal = document.getElementById('instagram-post-modal');
    modalContent = document.getElementById('instagram-modal-content');
    modalImage = document.getElementById('modal-post-image');
    modalCaption = document.getElementById('modal-post-caption');
    modalText = document.getElementById('modal-post-text');
    modalDate = document.getElementById('modal-post-date');
    modalLink = document.getElementById('modal-post-link');
    closeModalButton = document.getElementById('close-instagram-modal');
    prevPostButton = document.getElementById('prev-post');
    nextPostButton = document.getElementById('next-post');
    prevPostMobileButton = document.getElementById('prev-post-mobile');
    nextPostMobileButton = document.getElementById('next-post-mobile');
    showMoreTextButton = document.getElementById('show-more-text');
    
    // Funktion zum Laden der Instagram-Beiträge über unsere API
    async function loadInstagramPosts() {
        try {
            // Hier verwenden wir einen Proxy-Endpunkt, der auf Ihrem Server implementiert werden muss
            const response = await fetch(`/api/instagram-feed.php?username=${INSTAGRAM_USERNAME}&count=${POST_COUNT}`);
            
            if (!response.ok) {
                throw new Error('Fehler beim Laden der Instagram-Beiträge');
            }
            
            const data = await response.json();
            instagramPosts = data.posts || [];
            
            // Beiträge anzeigen
            renderInstagramPosts();
            
        } catch (error) {
            console.error('Instagram Feed Error:', error);
            showFallbackPosts();
        }
    }
    
    // Funktion zum Anzeigen der Beiträge
    function renderInstagramPosts() {
        // Lade-Animation entfernen
        const loadingElement = postsContainer.querySelector('.instagram-post-loading');
        if (loadingElement) {
            loadingElement.remove();
        }
        
        // Wenn keine Beiträge geladen wurden, Fallback anzeigen
        if (instagramPosts.length === 0) {
            showFallbackPosts();
            return;
        }
        
        // Beiträge anzeigen
        instagramPosts.forEach((post, index) => {
            const postElement = createPostElement(post, index);
            postsContainer.appendChild(postElement);
        });
    }
    
    // Funktion zum Erstellen eines Post-Elements
    function createPostElement(post, index) {
        const postElement = document.createElement('div');
        postElement.className = 'bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition duration-300';
        
        // Datum formatieren
        const postDate = new Date(post.timestamp * 1000);
        const formattedDate = postDate.toLocaleDateString('de-DE', {
            day: '2-digit',
            month: 'long',
            year: 'numeric'
        });
        
        // Caption kürzen (für die Vorschau)
        const shortCaption = post.caption.length > 60 
            ? post.caption.substring(0, 60) + '...' 
            : post.caption;
        
        // Medien-Element basierend auf Typ erstellen
        let mediaElement = '';
        if (post.type === 'VIDEO') {
            // Video-Element für Videos
            mediaElement = `
                <video src="${post.media_url}" alt="${shortCaption}" 
                       class="w-full h-64 object-cover" 
                       controls poster="${post.thumbnail_url || ''}" 
                       preload="metadata">
                    Ihr Browser unterstützt keine Videos.
                </video>`;
        } else {
            // Bild-Element für Bilder
            mediaElement = `
                <img src="${post.media_url}" alt="${shortCaption}" 
                     class="w-full h-64 object-cover">`;
        }
        
        postElement.innerHTML = `
            ${mediaElement}
            <div class="p-6">
                <div class="text-secondary text-sm mb-2">${formattedDate}</div>
                <h3 class="text-xl font-bold text-primary mb-3">${shortCaption}</h3>
                <p class="text-gray-600 mb-4">${post.type === 'VIDEO' ? 'Video: ' : ''}${shortCaption}</p>
                <a href="#" class="text-secondary font-bold hover:underline instagram-read-more" data-index="${index}">
                    <span data-translate="read_more">Weiterlesen</span> <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
        `;
        
        // Event-Listener für "Weiterlesen"
        const readMoreLink = postElement.querySelector('.instagram-read-more');
        readMoreLink.addEventListener('click', function(e) {
            e.preventDefault();
            openPostModal(index);
        });
        
        return postElement;
    }
    
    // Funktion zum Öffnen des Modals für einen Beitrag
    function openPostModal(index) {
        if (index < 0 || index >= instagramPosts.length) return;
        
        currentPostIndex = index;
        const post = instagramPosts[index];
        
        // Alle Videos in der Übersicht pausieren
        pauseAllVideosInFeed();
        
        // Text zurücksetzen (2 Zeilen auf Mobilgeräten)
        resetModalTextView();
        
        // Modal-Inhalt aktualisieren basierend auf Medientyp
        const modalMediaContainer = document.getElementById('modal-media-container');
        if (modalMediaContainer) {
            if (post.type === 'VIDEO') {
                // Video anzeigen
                modalMediaContainer.innerHTML = `
                    <video id="modal-post-video" src="${post.media_url}" 
                           controls autoplay 
                           class="w-full h-auto rounded-lg" 
                           poster="${post.thumbnail_url || ''}">
                        Ihr Browser unterstützt keine Videos.
                    </video>`;
            } else {
                // Bild anzeigen
                modalMediaContainer.innerHTML = `
                    <img id="modal-post-image" src="${post.media_url}" 
                         alt="${post.caption}" 
                         class="w-full h-auto rounded-lg object-cover object-top">`;
            }
        }
        
        // Caption in Titel und Text aufteilen
        const captionParts = post.caption.split('\n');
        const title = captionParts[0];
        const text = captionParts.slice(1).join('\n');
        
        // Titel und Text setzen
        const modalCaption = document.getElementById('modal-post-caption');
        const modalText = document.getElementById('modal-post-text');
        if (modalCaption) modalCaption.textContent = title;
        if (modalText) modalText.innerHTML = text.replace(/\n/g, '<br>');
        
        // Datum formatieren
        const postDate = new Date(post.timestamp * 1000);
        const modalDate = document.getElementById('modal-post-date');
        if (modalDate) {
            modalDate.textContent = postDate.toLocaleDateString('de-DE', {
                day: '2-digit',
                month: 'long',
                year: 'numeric'
            });
        }
        
        // Link zum Original-Post
        const modalLink = document.getElementById('modal-post-link');
        if (modalLink) modalLink.href = post.permalink;
        
        // Navigation-Buttons aktualisieren
        updateNavigationButtons();
        
        // Modal anzeigen
        const modal = document.getElementById('instagram-post-modal');
        if (modal) {
            modal.classList.remove('hidden');
            // Scroll verhindern
            document.body.style.overflow = 'hidden';
        }
    }
    
    // Funktion zum Schließen des Modals
    function closePostModal() {
        // Video im Modal pausieren, falls vorhanden
        const modalVideo = document.getElementById('modal-post-video');
        if (modalVideo) {
            modalVideo.pause();
        }
        
        modal.classList.add('hidden');
        document.body.style.overflow = '';
    }
    
    // Funktion zum Aktualisieren der Navigations-Buttons
    function updateNavigationButtons() {
        const isFirstPost = currentPostIndex === 0;
        const isLastPost = currentPostIndex === instagramPosts.length - 1;
        
        // Desktop Buttons
        if (prevPostButton) {
            prevPostButton.disabled = isFirstPost;
            prevPostButton.style.opacity = isFirstPost ? '0.5' : '1';
        }
        
        if (nextPostButton) {
            nextPostButton.disabled = isLastPost;
            nextPostButton.style.opacity = isLastPost ? '0.5' : '1';
        }
        
        // Mobile Buttons
        if (prevPostMobileButton) {
            prevPostMobileButton.disabled = isFirstPost;
            prevPostMobileButton.style.opacity = isFirstPost ? '0.5' : '1';
        }
        
        if (nextPostMobileButton) {
            nextPostMobileButton.disabled = isLastPost;
            nextPostMobileButton.style.opacity = isLastPost ? '0.5' : '1';
        }
    }
    
    // Funktion zum Anzeigen von Fallback-Beiträgen
    function showFallbackPosts() {
        postsContainer.innerHTML = `
            <div class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition duration-300">
                <img src="https://images.unsplash.com/photo-1521791136064-7986c2920216?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1469&q=80" alt="Politik" class="w-full h-48 object-cover">
                <div class="p-6">
                    <div class="text-secondary text-sm mb-2">12. März 2025</div>
                    <h3 class="text-xl font-bold text-primary mb-3" data-translate="news1_title">Neue Initiative für nachhaltige Stadtentwicklung</h3>
                    <p class="text-gray-600 mb-4" data-translate="news1_text">Vision für Lünen stellt Konzept für klimafreundliche Stadtquartiere vor.</p>
                    <a href="#" class="text-secondary font-bold hover:underline" data-translate="read_more">Weiterlesen <i class="fas fa-arrow-right ml-1"></i></a>
                </div>
            </div>
            <div class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition duration-300">
                <img src="/assets/images/bild.jpg" alt="Veranstaltung" class="w-full h-48 object-cover">
                <div class="p-6">
                    <div class="text-secondary text-sm mb-2">05. Januar 2025</div>
                    <h3 class="text-xl font-bold text-primary mb-3" data-translate="news2_title">Bürgerdialog zur Zukunft der Innenstadt</h3>
                    <p class="text-gray-600 mb-4" data-translate="news2_text">Diskutieren Sie mit uns über die Weiterentwicklung des Lüner Stadtzentrums.</p>
                    <a href="#" class="text-secondary font-bold hover:underline" data-translate="read_more">Weiterlesen <i class="fas fa-arrow-right ml-1"></i></a>
                </div>
            </div>
            <div class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition duration-300">
                <img src="https://images.unsplash.com/photo-1434030216411-0b793f4b4173?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80" alt="Team" class="w-full h-48 object-cover">
                <div class="p-6">
                    <div class="text-secondary text-sm mb-2">15. Dezember 2024</div>
                    <h3 class="text-xl font-bold text-primary mb-3" data-translate="news3_title">Neue Gesichter bei Vision für Lünen</h3>
                    <p class="text-gray-600 mb-4" data-translate="news3_text">Wir freuen uns über drei neue Mitglieder in unserem Team.</p>
                    <a href="#" class="text-secondary font-bold hover:underline" data-translate="read_more">Weiterlesen <i class="fas fa-arrow-right ml-1"></i></a>
                </div>
            </div>
        `;
    }
    
    // Event-Listener für die Buttons
    if (closeModalButton) {
        closeModalButton.addEventListener('click', function() {
            closePostModal();
        });
    }

    // Desktop Navigation
    if (prevPostButton) {
        prevPostButton.addEventListener('click', function() {
            if (currentPostIndex > 0) {
                // Aktuelles Video pausieren, falls vorhanden
                const modalVideo = document.getElementById('modal-post-video');
                if (modalVideo) {
                    modalVideo.pause();
                }
                
                openPostModal(currentPostIndex - 1);
            }
        });
    }

    if (nextPostButton) {
        nextPostButton.addEventListener('click', function() {
            if (currentPostIndex < instagramPosts.length - 1) {
                // Aktuelles Video pausieren, falls vorhanden
                const modalVideo = document.getElementById('modal-post-video');
                if (modalVideo) {
                    modalVideo.pause();
                }
                
                openPostModal(currentPostIndex + 1);
            }
        });
    }

    // Mobile Navigation
    if (prevPostMobileButton) {
        prevPostMobileButton.addEventListener('click', function() {
            if (currentPostIndex > 0) {
                // Aktuelles Video pausieren, falls vorhanden
                const modalVideo = document.getElementById('modal-post-video');
                if (modalVideo) {
                    modalVideo.pause();
                }
                
                openPostModal(currentPostIndex - 1);
            }
        });
    }

    if (nextPostMobileButton) {
        nextPostMobileButton.addEventListener('click', function() {
            if (currentPostIndex < instagramPosts.length - 1) {
                // Aktuelles Video pausieren, falls vorhanden
                const modalVideo = document.getElementById('modal-post-video');
                if (modalVideo) {
                    modalVideo.pause();
                }
                
                openPostModal(currentPostIndex + 1);
            }
        });
    }

    // Event-Listener für "Mehr anzeigen" Button
    if (showMoreTextButton) {
        showMoreTextButton.addEventListener('click', function() {
            if (modalText.classList.contains('line-clamp-2')) {
                modalText.classList.remove('line-clamp-2');
                this.textContent = 'Weniger anzeigen';
            } else {
                modalText.classList.add('line-clamp-2');
                this.textContent = 'Mehr anzeigen';
            }
        });
    }

    // Funktion zum Zurücksetzen der Textansicht im Modal
    function resetModalTextView() {
        if (modalText && showMoreTextButton) {
            modalText.classList.add('line-clamp-2');
            showMoreTextButton.textContent = 'Mehr anzeigen';
        }
    }

    // Klick außerhalb des Modal-Inhalts schließt das Modal
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closePostModal();
            }
        });
    }
    
    // ESC-Taste schließt das Modal
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
            closePostModal();
        }
    });
    
    // Instagram-Beiträge laden
    if (postsContainer) {
        loadInstagramPosts();
    }
});

// "Mehr anzeigen" Button Funktionalität
document.getElementById('show-more-text').addEventListener('click', function() {
    const textElement = document.getElementById('modal-post-text');
    const showMoreButton = this;
    
    if (textElement.classList.contains('line-clamp-2')) {
        // Text erweitern
        textElement.classList.remove('line-clamp-2');
        textElement.classList.add('expanded-text');
        
        // Button-Text ändern
        showMoreButton.setAttribute('data-translate', 'show_less');
        if (typeof i18nData !== 'undefined' && i18nData[currentLanguage] && i18nData[currentLanguage]['show_less']) {
            showMoreButton.textContent = i18nData[currentLanguage]['show_less'];
        } else {
            showMoreButton.textContent = 'Weniger anzeigen';
        }
    } else {
        // Text reduzieren
        textElement.classList.add('line-clamp-2');
        textElement.classList.remove('expanded-text');
        
        // Button-Text ändern
        showMoreButton.setAttribute('data-translate', 'show_more');
        if (typeof i18nData !== 'undefined' && i18nData[currentLanguage] && i18nData[currentLanguage]['show_more']) {
            showMoreButton.textContent = i18nData[currentLanguage]['show_more'];
        } else {
            showMoreButton.textContent = 'Mehr anzeigen';
        }
    }
});

// Beim Öffnen des Modals den Text wieder auf 2 Zeilen zurücksetzen
function resetModalTextView() {
    const textElement = document.getElementById('modal-post-text');
    const showMoreButton = document.getElementById('show-more-text');
    
    if (textElement && showMoreButton) {
        textElement.classList.add('line-clamp-2');
        textElement.classList.remove('expanded-text');
        
        // Button-Text zurücksetzen
        showMoreButton.setAttribute('data-translate', 'show_more');
        if (typeof i18nData !== 'undefined' && i18nData[currentLanguage] && i18nData[currentLanguage]['show_more']) {
            showMoreButton.textContent = i18nData[currentLanguage]['show_more'];
        } else {
            showMoreButton.textContent = 'Mehr anzeigen';
        }
    }
}

// Funktion aufrufen, wenn ein Post im Modal geöffnet wird
function openPostInModal(post, index) {
    // Bestehender Code...
    
    // Text zurücksetzen
    resetModalTextView();
    
    // Bestehender Code...
}

// Funktion zum Pausieren aller Videos in der Feed-Übersicht
function pauseAllVideosInFeed() {
    if (!postsContainer) return; // Sicherheitscheck
    
    const feedVideos = postsContainer.querySelectorAll('video');
    feedVideos.forEach(video => {
        if (!video.paused) {
            video.pause();
        }
    });
}
