function setupTabs() {
    const tabs = document.querySelectorAll('.tab');
    const tabContents = document.querySelectorAll('.tab-content');

    if (tabs.length === 0 || tabContents.length === 0) {
        // console.info("No tabs found on this page."); // <PERSON><PERSON>, nur Info
        return;
    }

    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // Deaktiviere alle Tabs und Inhalte
            tabs.forEach(t => t.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));

            // Aktiviere den geklickten Tab und den zugehörigen Inhalt
            tab.classList.add('active');
            const tabId = tab.getAttribute('data-tab');
            const targetContent = document.getElementById(`${tabId}-tab`);

            if (targetContent) {
                targetContent.classList.add('active');

                // <PERSON><PERSON><PERSON>, das reCAPTCHA im aktivierten Tab zurückzusetzen
                const recaptchaWidget = targetContent.querySelector('.g-recaptcha');
                if (recaptchaWidget && typeof grecaptcha !== 'undefined' && grecaptcha.render) { // Prüfen ob reCAPTCHA API bereit ist
                     try {
                        // Finde die Widget-ID (wird von der API oft hinzugefügt)
                        const widgetId = recaptchaWidget.getAttribute('data-widget-id');
                        if (widgetId) {
                           grecaptcha.reset(widgetId);
                        } else {
                           // Fallback: Versuche, das erste Widget zu resetten, falls keine ID da ist
                           // Dies ist weniger zuverlässig, wenn mehrere Widgets ohne explizite IDs existieren.
                           // Prüfe, ob schon gerendert wurde (hat Inhalt)
                           if (recaptchaWidget.innerHTML !== '') {
                              grecaptcha.reset();
                           }
                        }
                     } catch(e) {
                        console.warn("Could not reset reCAPTCHA on tab switch:", e);
                     }
                }
            } else {
                console.error(`Tab content with ID ${tabId}-tab not found.`);
            }
        });
    });

    // Sicherstellen, dass der initial aktive Tab auch seinen Inhalt anzeigt
    const initialActiveTab = document.querySelector('.tab.active');
    if (initialActiveTab) {
        const initialTabId = initialActiveTab.getAttribute('data-tab');
        const initialTargetContent = document.getElementById(`${initialTabId}-tab`);
        if (initialTargetContent) {
            initialTargetContent.classList.add('active');
        }
    } else if (tabs.length > 0) {
        // Fallback: Wenn kein Tab initial aktiv ist, aktiviere den ersten
        tabs[0].click();
    }
}