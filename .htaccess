RewriteEngine On

# Blockiere direkten Zugriff auf sensible Verzeichnisse
RewriteRule ^(includes|sql|cgi-bin)/ - [F,L]

<Files .htaccess>
    Order allow,deny
    Deny from all
</Files>

<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|lock)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# >>> Zug<PERSON> verhindern, wenn jemand über vision-luenen.de/test/ zugreift <<<
RewriteCond %{HTTP_HOST} ^(www\.)?vision-luenen\.de$ [NC]
RewriteRule ^test/ - [F,L]

# >>> Verwaltung Subdomain: alles auf /verwaltung/ routen
RewriteCond %{HTTP_HOST} ^verwaltung\.vision-luenen\.de$ [NC]
RewriteCond %{REQUEST_URI} !^/verwaltung/
RewriteRule ^(.*)$ /verwaltung/$1 [L,QSA]

<IfModule mod_headers.c>
    # Erlaube Anfragen von deiner Verwaltungs-Subdomain
    # WICHTIG: Passe Protokoll/Domain an!
    Header set Access-Control-Allow-Origin "https://verwaltung.vision-luenen.de"

    # Erlaube Methoden
    Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"

    # Erlaube Header
    Header set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"

    # Spezielle Behandlung für OPTIONS (Preflight) Requests
    RewriteEngine On
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=204,L] # Antworte mit 204 No Content und stoppe
</IfModule>

# Standard-Dokument
DirectoryIndex index.php
