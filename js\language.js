// js/language.js

// i18nData wird jetzt direkt aus i18n.js importiert
// Standardsprache setzen
let currentLanguage = defaultLanguage || 'de';

// Die loadTranslations Funktion kann vereinfacht werden, da die Daten bereits geladen sind
async function loadTranslations() {
    // Keine Notwendigkeit, die Übersetzungen zu laden, da sie bereits in i18n.js definiert sind
    return Promise.resolve();
}

function changeLanguage(lang) {
    if (i18nData[lang]) {
        currentLanguage = lang;
        updatePageLanguage();
        try {
            localStorage.setItem('preferredLanguage', lang);
            
            // Cookie setzen für serverseitige Erkennung
            document.cookie = "user_language=" + lang + "; path=/; max-age=" + (86400 * 30); // 30 Tage
            
            // Optional: Session-Cookie über AJAX setzen
            if (window.fetch) {
                fetch('php/set_language.php?lang=' + lang, {
                    method: 'GET',
                    credentials: 'same-origin'
                }).catch(e => console.error('Fehler beim Setzen der Sprache:', e));
            }
        } catch(e) {
            console.error('Fehler beim Speichern der Sprachpräferenz:', e);
        }
    }
}

// Funktion zum Umleiten mit Sprachparameter
function redirectWithLanguage(lang) {
    if (i18nData[lang]) {
        // Aktuelle URL holen
        let url = new URL(window.location.href);
        // Lang-Parameter setzen
        url.searchParams.set('lang', lang);
        // Umleiten
        window.location.href = url.toString();
    }
}

function updatePageLanguage() {
    const langData = i18nData[currentLanguage];
    if (!langData) {
        return;
    }

    // Textinhalte übersetzen (data-translate)
    document.querySelectorAll('[data-translate]').forEach(element => {
        const key = element.getAttribute('data-translate');
        if (langData[key] !== undefined) {
            element.innerHTML = langData[key];
        }
    });

    // Placeholder übersetzen (data-translate-placeholder)
    document.querySelectorAll('[data-translate-placeholder]').forEach(element => {
        const key = element.getAttribute('data-translate-placeholder');
        if (langData[key] !== undefined) {
            element.placeholder = langData[key];
        }
    });

    document.documentElement.lang = currentLanguage;
    if (currentLanguage === 'ar') {
        document.documentElement.dir = 'rtl';
        document.body.classList.add('rtl');
    } else {
        document.documentElement.dir = 'ltr';
        document.body.classList.remove('rtl');
    }

    const langSelectorText = document.querySelector('.language-selector span[data-translate="language"]');
    if(langSelectorText && langData['language']) {
        langSelectorText.textContent = langData['language'];
    }

     if (langData['title']) {
        document.title = langData['title'];
     }
}

function setupLanguageSwitcher() {
     document.querySelectorAll('.lang-link').forEach(link => {
         link.removeEventListener('click', handleLanguageLinkClick);
         link.addEventListener('click', handleLanguageLinkClick);
     });

     const langButton = document.querySelector('.language-selector > button');
     const langDropdown = document.querySelector('.language-dropdown');
     if(langButton && langDropdown) {
        langButton.removeEventListener('click', toggleLanguageDropdown);
        document.removeEventListener('click', closeLanguageDropdownOnClickOutside);
        langButton.addEventListener('click', toggleLanguageDropdown);
        document.addEventListener('click', closeLanguageDropdownOnClickOutside);
     }
}

function handleLanguageLinkClick(e) {
    e.preventDefault();
    const lang = e.currentTarget.getAttribute('data-lang');
    changeLanguage(lang);
    
    // Optional: Wenn Sie möchten, dass die Seite neu geladen wird, um die URL zu aktualisieren
    // window.location.href = '?lang=' + lang;
}

function toggleLanguageDropdown(e) {
    e.stopPropagation();
    const langDropdown = document.querySelector('.language-dropdown');
    if(langDropdown) langDropdown.classList.toggle('show');
}

function closeLanguageDropdownOnClickOutside(event) {
     const langButton = document.querySelector('.language-selector > button');
     const langDropdown = document.querySelector('.language-dropdown');
     if(langDropdown && langDropdown.classList.contains('show') && !langButton.contains(event.target) && !langDropdown.contains(event.target)) {
        langDropdown.classList.remove('show');
    }
}

// Die Initialisierung erfolgt durch js/main.js
