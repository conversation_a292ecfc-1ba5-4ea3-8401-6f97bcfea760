<?php
header('Content-Type: application/json');

require_once __DIR__ . '/config.php';
require_once BASE_PATH . '/php/db_connect.php';
require_once BASE_PATH . '/php/recaptcha_verify.php';

// JSON-Daten aus dem Request-Body lesen
$jsonData = file_get_contents('php://input');
$data = json_decode($jsonData, true);

// Antwort vorbereiten
$response = ['success' => false];

// reCAPTCHA prüfen
if (!isset($data['recaptcha_token']) || !verifyRecaptcha($data['recaptcha_token'])) {
    $response['message'] = 'reCAPTCHA-Validierung fehlgeschlagen.';
    echo json_encode($response);
    exit;
}

// Daten validieren
if (!isset($data['amount']) || !is_numeric($data['amount']) || $data['amount'] <= 0) {
    $response['message'] = 'Ungültiger Spendenbetrag.';
    echo json_encode($response);
    exit;
}

if (!isset($data['firstname']) || !isset($data['lastname']) || !isset($data['email'])) {
    $response['message'] = 'Unvollständige Spenderdaten.';
    echo json_encode($response);
    exit;
}

// PayPal API-Konfiguration
$paypalMode = PAYPAL_MODE;
$clientId = PAYPAL_CLIENT_ID;
$clientSecret = PAYPAL_CLIENT_SECRET;

// API-URLs basierend auf dem Modus
$apiUrl = ($paypalMode === 'sandbox') 
    ? 'https://api-m.sandbox.paypal.com' 
    : 'https://api-m.paypal.com';

// Access Token von PayPal holen
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl . '/v1/oauth2/token');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, 'grant_type=client_credentials');
curl_setopt($ch, CURLOPT_USERPWD, $clientId . ':' . $clientSecret);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);

$result = curl_exec($ch);
if (curl_errno($ch)) {
    $response['message'] = 'Fehler bei der Verbindung zu PayPal: ' . curl_error($ch);
    echo json_encode($response);
    curl_close($ch);
    exit;
}

$authResponse = json_decode($result, true);
curl_close($ch);

if (!isset($authResponse['access_token'])) {
    $response['message'] = 'Fehler bei der Authentifizierung mit PayPal.';
    echo json_encode($response);
    exit;
}

$accessToken = $authResponse['access_token'];

// Order bei PayPal erstellen
$amount = number_format((float)$data['amount'], 2, '.', '');
$orderPayload = [
    'intent' => 'CAPTURE',
    'purchase_units' => [
        [
            'amount' => [
                'currency_code' => 'EUR',
                'value' => $amount
            ],
            'description' => 'Spende an Vision Lünen',
            'custom_id' => 'Spende von ' . $data['firstname'] . ' ' . $data['lastname'] . ' an Vision für Lünen'
        ]
    ],
    'application_context' => [
        'brand_name' => 'Vision Lünen',
        'landing_page' => 'BILLING',
        'user_action' => 'PAY_NOW',
        'return_url' => 'https://' . $_SERVER['HTTP_HOST'] . '/php/paypal_success.php',
        'cancel_url' => 'https://' . $_SERVER['HTTP_HOST'] . '/php/paypal_cancel.php'
    ]
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl . '/v2/checkout/orders');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($orderPayload));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: Bearer ' . $accessToken
]);

$result = curl_exec($ch);
if (curl_errno($ch)) {
    $response['message'] = 'Fehler bei der Erstellung der PayPal-Order: ' . curl_error($ch);
    echo json_encode($response);
    curl_close($ch);
    exit;
}

$orderResponse = json_decode($result, true);
curl_close($ch);

if (!isset($orderResponse['id'])) {
    $response['message'] = 'Fehler bei der Erstellung der PayPal-Order.';
    if (isset($orderResponse['error'])) {
        $response['debug'] = $orderResponse['error'];
    }
    echo json_encode($response);
    exit;
}

// Spenderdaten in der Datenbank speichern
try {
    // Transaktion starten
    $pdo->beginTransaction();
    
    // Prüfen, ob der Spender bereits existiert und ob er bereits exportiert wurde
    $checkStmt = $pdo->prepare("
        SELECT spender_id, person_exportiert, externemitgliedsnummer
        FROM spender 
        WHERE email = :email 
        ORDER BY erstellt_am DESC 
        LIMIT 1
    ");
    
    $checkStmt->execute([':email' => $data['email']]);
    $existingDonor = $checkStmt->fetch(PDO::FETCH_ASSOC);
    
    // Export-Status bestimmen
    $exportStatus = false;
    if ($existingDonor && $existingDonor['person_exportiert']) {
        $exportStatus = true;
    }
    
    // Neue Spende in der Datenbank speichern
    $stmt = $pdo->prepare("
        INSERT INTO spender (
            vorname, 
            name, 
            email, 
            bereitsmitglied, 
            individuellerbeitrag,
            zahlungsart, 
            transaktions_id, 
            status, 
            person_exportiert,
            zusatzfeld_organisation,
            geburtsdatum,
            telefonprivat,
            strasse,
            plz,
            ort,
            staat,
            zusatzfeld_datenschutz,
            zusatzfeld_richtigkeit,
            adresstyp
        ) VALUES (
            :vorname, 
            :nachname, 
            :email, 
            :ist_mitglied, 
            :betrag,
            'PayPal', 
            :transaktions_id, 
            'ausstehend',
            :person_exportiert,
            :organisation,
            :geburtsdatum,
            :telefon,
            :strasse,
            :plz,
            :ort,
            :land,
            :datenschutz,
            :richtigkeit,
            :adresstyp
        )
    ");

    // Adresstyp basierend auf Mitgliedschaftsstatus setzen
    $adresstyp = (isset($data['is_member']) && $data['is_member']) ? 1 : 2;

    $stmt->execute([
        ':vorname' => $data['firstname'],
        ':nachname' => $data['lastname'],
        ':email' => $data['email'],
        ':ist_mitglied' => isset($data['is_member']) && $data['is_member'] ? 1 : 0,
        ':betrag' => $amount,
        ':transaktions_id' => $orderResponse['id'],
        ':person_exportiert' => $exportStatus ? 1 : 0,
        ':organisation' => $data['organisation'] ?? null,
        ':geburtsdatum' => $data['geburtsdatum'] ?? null,
        ':telefon' => $data['telefon'] ?? null,
        ':strasse' => $data['strasse'] ?? null,
        ':plz' => $data['plz'] ?? null,
        ':ort' => $data['ort'] ?? null,
        ':land' => $data['land'] ?? 'Deutschland',
        ':datenschutz' => isset($data['datenschutz']) && $data['datenschutz'] ? 1 : 0,
        ':richtigkeit' => isset($data['richtigkeit']) && $data['richtigkeit'] ? 1 : 0,
        ':adresstyp' => $adresstyp
    ]);
    
    // Spender-ID abrufen
    $spenderId = $pdo->lastInsertId();
    
    // Externe Mitgliedsnummer generieren (NMGD-XXXXX)
    $externemitgliedsnummer = 'NMGD-' . str_pad($spenderId, 5, '0', STR_PAD_LEFT);
    
    // Externe Mitgliedsnummer aktualisieren
    $updateStmt = $pdo->prepare("
        UPDATE spender 
        SET externemitgliedsnummer = :externemitgliedsnummer 
        WHERE spender_id = :spender_id
    ");
    
    $updateStmt->execute([
        ':externemitgliedsnummer' => $externemitgliedsnummer,
        ':spender_id' => $spenderId
    ]);
    
    // Transaktion abschließen
    $pdo->commit();
    
    // Erfolgreiche Antwort senden
    $approvalUrl = '';
    foreach ($orderResponse['links'] as $link) {
        if ($link['rel'] === 'approve') {
            $approvalUrl = $link['href'];
            break;
        }
    }
    
    $response = [
        'success' => true,
        'order_id' => $orderResponse['id'],
        'approval_url' => $approvalUrl
    ];
    
} catch (PDOException $e) {
    // Bei Fehler Transaktion zurückrollen
    $pdo->rollBack();
    $response['message'] = 'Datenbankfehler: ' . $e->getMessage();
}

echo json_encode($response);
