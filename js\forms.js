// js/forms.js

// --- Globale Hilfsfunktion für die Formularvalidierung ---
function validateForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    const errorMessages = form.querySelectorAll('.error-message'); // Annahme: Fehlermeldungen haben diese Klasse

    // Reset previous errors
    errorMessages.forEach(p => p.classList.add('hidden')); // Verstecke alle Fehlermeldungen im Formular
    form.querySelectorAll('.input-error').forEach(el => el.classList.remove('input-error'));
    form.querySelectorAll('label.text-red-500').forEach(el => el.classList.remove('text-red-500')); // Alte Methode, ggf. entfernen

    requiredFields.forEach(field => {
        let fieldIsValid = true;
        let errorMessageElement = form.querySelector(`[id="${field.id}-error"]`) || form.querySelector(`#${field.getAttribute('aria-describedby')}`); // Finde Fehlermeldungs-Element per ID oder aria

        if (field.type === 'checkbox' && !field.checked) {
            fieldIsValid = false;
        } else if (field.type !== 'checkbox' && !field.value.trim()) {
            fieldIsValid = false;
        } else if (field.pattern && field.value.trim() && !new RegExp(`^(?:${field.pattern})$`).test(field.value)) { // Prüfe Pattern nur, wenn Wert vorhanden
            fieldIsValid = false;
        } else if (field.type === 'email' && field.value.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(field.value)) {
             fieldIsValid = false;
        } else if (field.type === 'date' && field.max && field.value > field.max) {
            fieldIsValid = false;
        } else if (field.type === 'number' && field.min && parseFloat(field.value) < parseFloat(field.min)) {
            // Min value für Zahlen (z.B. Beitrag)
            fieldIsValid = false;
        }

        if (!fieldIsValid) {
            isValid = false;
            field.classList.add('input-error', 'border-red-500'); // Visuelles Feedback
            // Label rot machen (optional)
            const label = form.querySelector(`label[for='${field.id}']`);
            if (label) {
                label.classList.add('text-red-500');
            }
            // Zugehörige Fehlermeldung anzeigen
            if(errorMessageElement) {
                errorMessageElement.classList.remove('hidden');
                 // Hole generische oder spezifische Fehlermeldung (müsste in translations.json definiert sein)
                const errorKey = field.dataset.errorKey || 'error_validation_generic'; // Beispiel für spezifischen Key
                errorMessageElement.textContent = i18nData[currentLanguage]?.[errorKey] || 'Ungültige Eingabe.';
            }
        } else {
             field.classList.remove('input-error', 'border-red-500');
             const label = form.querySelector(`label[for='${field.id}']`);
             if (label) {
                label.classList.remove('text-red-500');
             }
             if(errorMessageElement) {
                 errorMessageElement.classList.add('hidden');
             }
        }
    });
    return isValid;
}

// --- reCAPTCHA Validierung ---
function validateRecaptcha(form) {
    const recaptchaContainer = form.querySelector('.g-recaptcha');
    const errorElement = form.querySelector('[id$="-recaptcha-error"]'); // Sucht nach ID, die auf -recaptcha-error endet
    let isValid = true;

    // Verstecke Fehler zuerst
    if (errorElement) errorElement.classList.add('hidden');

    if (recaptchaContainer && typeof grecaptcha !== 'undefined' && grecaptcha.getResponse) {
        // Finde die Widget-ID (wird oft beim Rendern gesetzt)
        let widgetId = undefined;
        // Workaround, falls dataset.widgetid nicht gesetzt ist (API v2)
        // Manchmal ist es das erste/einzige Widget auf der Seite
        // Dies ist nicht 100% robust, wenn mehrere reCAPTCHAs existieren.
        try {
            if(window.___grecaptcha_cfg && window.___grecaptcha_cfg.clients) {
                 const siteKey = recaptchaContainer.dataset.sitekey;
                 const clients = window.___grecaptcha_cfg.clients;
                 for (const id in clients) {
                     if (clients.hasOwnProperty(id)) {
                        // Annahme: Das Widget im Formular ist das relevante
                        // Robuster wäre, die ID beim Rendern zu speichern.
                        widgetId = parseInt(id); // Oft numerisch
                        break; // Nimm das erste gefundene
                     }
                 }
            }
        } catch(e) { console.warn("Error trying to find reCAPTCHA widget ID:", e); }


        const response = grecaptcha.getResponse(widgetId); // Versuche es mit oder ohne ID
        if (!response) {
            if (errorElement) {
                errorElement.textContent = i18nData[currentLanguage]?.recaptcha_error || 'Bitte bestätigen Sie, dass Sie kein Roboter sind.';
                errorElement.classList.remove('hidden');
            }
            isValid = false;
        }
    } else if (recaptchaContainer) {
        console.warn("reCAPTCHA API not ready or widget not fully initialized.");
        if (errorElement) {
             errorElement.textContent = i18nData[currentLanguage]?.recaptcha_error || 'reCAPTCHA nicht bereit.'; // Andere Meldung
             errorElement.classList.remove('hidden');
        }
        isValid = false;
    }
    return isValid;
}


// --- Scrollen zum ersten Fehler ---
function scrollToFirstError(form) {
    const firstErrorField = form.querySelector('.input-error'); // Finde das Feld
    if (firstErrorField) {
        // Finde das zugehörige Label oder das Feld selbst zum Scrollen
        const elementToScrollTo = form.querySelector(`label[for='${firstErrorField.id}']`) || firstErrorField;
        if (elementToScrollTo) {
            const header = document.querySelector('header.sticky') || document.querySelector('header');
            const headerOffset = header ? header.offsetHeight + 20 : 100; // Mehr Offset für Sichtbarkeit
            const elementPosition = elementToScrollTo.getBoundingClientRect().top;
            const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

            window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth'
            });
            // Fokus auf das fehlerhafte Feld setzen
            setTimeout(() => firstErrorField.focus({ preventScroll: true }), 300); // Leichte Verzögerung für den Fokus
        }
    } else {
         // Fallback: Scrolle zum reCAPTCHA-Fehler, falls dieser der einzige ist
         const recaptchaError = form.querySelector('[id$="-recaptcha-error"]:not(.hidden)');
         if (recaptchaError) {
             recaptchaError.scrollIntoView({ behavior: 'smooth', block: 'center' });
         }
    }
}


// --- Mitgliedschaftsformular einrichten ---
function setupMembershipForm() {
    const form = document.getElementById('membership-form');
    if (!form) return;

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validierung für dieses spezifische Formular aufrufen
        const isFormValid = validateForm(form);
        const isRecaptchaValid = validateRecaptcha(form);

        if (!isFormValid || !isRecaptchaValid) {
            // Zeige allgemeine Validierungsfehlermeldung
            Swal.fire({
                icon: 'error',
                title: i18nData[currentLanguage]?.form_error_title || 'Fehler!',
                text: i18nData[currentLanguage]?.error_validation || 'Bitte prüfen Sie Ihre Eingaben.',
            });
            // Scrolle zum ersten sichtbaren Fehler
            scrollToFirstError(form);
            return; // Stoppt die Ausführung
        }

        // Zeige Lade-Spinner oder deaktiviere Button
        const submitButton = form.querySelector('button[type="submit"]');
        const originalButtonText = submitButton.innerHTML;
        submitButton.disabled = true;
        submitButton.innerHTML = `<i class="fas fa-spinner fa-spin mr-2"></i> Wird gesendet...`;

        const formData = new FormData(form);

        fetch(form.action, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            // ========================================
            // === NEU: Debug-Ausgabe hinzufügen ===
            // ========================================
            if (data.debug && Array.isArray(data.debug)) {
                console.groupCollapsed(`--- Server Debug Log (${new Date().toLocaleTimeString()}) ---`);
                data.debug.forEach(msg => console.log(msg));
                console.groupEnd();
            }
            // ========================================
            // === ENDE Debug-Ausgabe           ===
            // ========================================

            // Reset reCAPTCHA hier, falls nötig, *bevor* SweetAlert angezeigt wird
            const recaptchaWidget = form.querySelector('.g-recaptcha');
            let widgetId = undefined;
             try {
                 if(window.___grecaptcha_cfg && window.___grecaptcha_cfg.clients) {
                     const clients = window.___grecaptcha_cfg.clients;
                     for (const id in clients) { if (clients.hasOwnProperty(id)) { widgetId = parseInt(id); break; }}
                 }
                 if (typeof grecaptcha !== 'undefined' && widgetId !== undefined) {
                    grecaptcha.reset(widgetId);
                 }
             } catch(e){ console.warn("Could not reset reCAPTCHA on response:", e);}


            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: i18nData[currentLanguage]?.form_success_title || 'Erfolgreich!',
                    text: i18nData[currentLanguage]?.form_success_text || 'Antrag erfolgreich gesendet.',
                    confirmButtonColor: '#4f46e5'
                });
                form.reset();
            } else {
                // Fehlermeldung anzeigen
                const errorCode = data.error_code || 'unknown';
                const errorMsgKey = `error_${errorCode}`;
                let specificMsg = i18nData[currentLanguage]?.[errorMsgKey];

                // Falls eine detaillierte Fehlermeldung für Validierung vorhanden ist
                if (errorCode === 'validation_failed' && data.errors && Array.isArray(data.errors)) {
                    specificMsg = (i18nData[currentLanguage]?.error_validation || 'Validierungsfehler:') + '\n - ' + data.errors.join('\n - ');
                }

                // Fallback auf generische oder unbekannte Fehlermeldung
                if (!specificMsg) {
                    specificMsg = i18nData[currentLanguage]?.error_unknown || 'Ein unbekannter Fehler ist aufgetreten.';
                }

                // Zusätzliche Server-Nachricht anhängen (optional, für Debugging)
                // if (data.message && data.error_code !== 'validation_failed') {
                //     specificMsg += ` (${data.message})`;
                // }

                Swal.fire({
                    icon: 'error',
                    title: i18nData[currentLanguage]?.form_error_title || 'Fehler!',
                    text: specificMsg,
                    confirmButtonColor: '#d33'
                });
            }
        })
        .catch(error => {
            console.error('Form submission fetch error:', error);
            // Reset reCAPTCHA auch bei Netzwerkfehler
            const recaptchaWidget = form.querySelector('.g-recaptcha');
            let widgetId = undefined;
             try {
                 if(window.___grecaptcha_cfg && window.___grecaptcha_cfg.clients) {
                     const clients = window.___grecaptcha_cfg.clients;
                     for (const id in clients) { if (clients.hasOwnProperty(id)) { widgetId = parseInt(id); break; }}
                 }
                 if (typeof grecaptcha !== 'undefined' && widgetId !== undefined) {
                    grecaptcha.reset(widgetId);
                 }
             } catch(e){ console.warn("Could not reset reCAPTCHA on fetch error:", e);}

            Swal.fire({
                icon: 'error',
                title: i18nData[currentLanguage]?.form_error_title || 'Fehler!',
                text: i18nData[currentLanguage]?.error_db || 'Verbindungsproblem oder Serverfehler. Bitte versuchen Sie es später erneut.',
                confirmButtonColor: '#d33'
            });
        })
        .finally(() => {
             submitButton.disabled = false;
             submitButton.innerHTML = originalButtonText;
        });
    });
}

// --- Spendenformular einrichten (Buttons) ---
function setupDonationForm() {
    const form = document.getElementById('donation-form');
    if (!form) return;

    const paypalButton = document.getElementById('paypal-button');
    const sofortButton = document.getElementById('sofort-button');

    // Event-Listener für die Buttons
    if (paypalButton) {
        paypalButton.addEventListener('click', handlePayPalDonation);
    } else {
        console.warn("PayPal-Button nicht gefunden!");
    }
    
    if (sofortButton) {
        sofortButton.addEventListener('click', handleSofortDonation);
    }
}

// PayPal-Button-Handler
const handlePayPalDonation = () => {
    const form = document.getElementById('donation-form');
    if (!form) return;
    
    console.log("PayPal-Button geklickt");
    
    // Nur die für PayPal wirklich notwendigen Felder prüfen
    const amount = form.querySelector('#donation-amount');
    const email = form.querySelector('#donation-email');
    
    let errors = [];
    
    // Nur minimale Validierung für PayPal
    if (!amount || !amount.value || parseFloat(amount.value) <= 0) {
        errors.push('Bitte geben Sie einen gültigen Spendenbetrag ein.');
        if (amount) amount.classList.add('input-error', 'border-red-500');
    }
    
    if (!email || !email.value || !email.value.includes('@')) {
        errors.push('Bitte geben Sie eine gültige E-Mail-Adresse ein.');
        if (email) email.classList.add('input-error', 'border-red-500');
    }
    
    // reCAPTCHA prüfen mit verbesserter Logik
    let recaptchaResponse = '';
    
    // Methode 1: Standard-API-Aufruf
    if (typeof grecaptcha !== 'undefined' && grecaptcha.getResponse) {
        recaptchaResponse = grecaptcha.getResponse();
        console.log("reCAPTCHA Antwort (Methode 1):", recaptchaResponse ? "vorhanden" : "leer");
    }
    
    // Methode 2: Widget-ID finden und verwenden
    if (!recaptchaResponse && typeof grecaptcha !== 'undefined') {
        try {
            const recaptchaContainer = form.querySelector('.g-recaptcha');
            let widgetId = undefined;
            
            if (window.___grecaptcha_cfg && window.___grecaptcha_cfg.clients) {
                const clients = window.___grecaptcha_cfg.clients;
                for (const id in clients) {
                    if (clients.hasOwnProperty(id)) {
                        widgetId = parseInt(id);
                        break;
                    }
                }
            }
            
            if (widgetId !== undefined) {
                recaptchaResponse = grecaptcha.getResponse(widgetId);
                console.log("reCAPTCHA Antwort (Methode 2):", recaptchaResponse ? "vorhanden" : "leer");
            }
        } catch (e) {
            console.error("Fehler beim Abrufen der reCAPTCHA-Antwort:", e);
        }
    }
    
    // Methode 3: Direkt aus dem Formular
    if (!recaptchaResponse) {
        const recaptchaField = form.querySelector('[name="g-recaptcha-response"]');
        if (recaptchaField && recaptchaField.value) {
            recaptchaResponse = recaptchaField.value;
            console.log("reCAPTCHA Antwort (Methode 3):", "vorhanden");
        }
    }
    
    const isRecaptchaValid = recaptchaResponse && recaptchaResponse.length > 0;
    
    if (!isRecaptchaValid) {
        errors.push('Bitte bestätigen Sie, dass Sie kein Roboter sind.');
        const recaptchaError = document.getElementById('donation-recaptcha-error');
        if (recaptchaError) recaptchaError.classList.remove('hidden');
    }
    
    // Bei Fehlern Meldung anzeigen und abbrechen
    if (errors.length > 0) {
        Swal.fire({
            icon: 'error',
            title: 'Für die PayPal-Zahlung fehlen Angaben',
            html: errors.join('<br>'),
            confirmButtonColor: '#d33'
        });
        return;
    }
    
    // Formular-Daten für PayPal sammeln
    const formData = new FormData(form);
    
    // Zeige Lade-Spinner
    Swal.fire({
        title: 'PayPal wird geladen...',
        text: 'Bitte warten...',
        icon: 'info',
        showConfirmButton: false,
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    // Sende nur die notwendigen Daten an den Server
    fetch('/php/create_paypal_order.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            amount: formData.get('individuellerbeitrag'),
            firstname: formData.get('vorname') || '',
            lastname: formData.get('name') || '',
            email: formData.get('email'),
            is_member: formData.get('bereitsmitglied') === '1',
            recaptcha_token: recaptchaResponse,
            // Zusätzliche Felder
            organisation: formData.get('zusatzfeld_organisation') || '',
            geburtsdatum: formData.get('geburtsdatum') || '',
            telefon: formData.get('telefonprivat') || '',
            strasse: formData.get('strasse') || '',
            plz: formData.get('plz') || '',
            ort: formData.get('ort') || '',
            land: formData.get('staat') || 'Deutschland',
            // Checkbox-Felder
            datenschutz: formData.get('zusatzfeld_datenschutz') ? 1 : 0,
            richtigkeit: formData.get('zusatzfeld_richtigkeit') ? 1 : 0
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.approval_url) {
            // Weiterleitung zur PayPal-Checkout-Seite
            window.location.href = data.approval_url;
        } else {
            // Fehler anzeigen
            Swal.fire({
                icon: 'error',
                title: 'Fehler',
                text: data.message || 'Es ist ein Fehler bei der Initiierung der PayPal-Zahlung aufgetreten.',
                confirmButtonColor: '#d33'
            });
        }
    })
    .catch(error => {
        console.error('PayPal order creation error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Fehler',
            text: 'Es ist ein Fehler bei der Verbindung zum Server aufgetreten. Bitte versuchen Sie es später erneut.',
            confirmButtonColor: '#d33'
        });
    });
};

// --- Smooth Scrolling für interne Links (Keine Änderungen hier nötig) ---
function setupSmoothScroll() {
    // ... (Code aus deiner Version kann bleiben)
    document.querySelectorAll('a.scroll-link').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            const href = anchor.getAttribute('href');
            if (href && href.startsWith('#')) {
                const targetId = href.substring(1);
                const targetElement = document.getElementById(targetId);
                // Prüfe, ob es ein Link zu dynamischem Inhalt ist (wird jetzt von main.js gehandhabt)
                if (targetId === 'datenschutz' || targetId === 'impressum') {
                    // Nichts tun, main.js' handleDelegatedScrollLinkClick kümmert sich darum
                } else if (targetElement) {
                    e.preventDefault();
                    const header = document.querySelector('header.sticky') || document.querySelector('header');
                    const headerOffset = header ? header.offsetHeight + 20 : 100;
                    const elementPosition = targetElement.getBoundingClientRect().top;
                    const offsetPosition = elementPosition + window.pageYOffset - headerOffset;
                    window.scrollTo({
                        top: offsetPosition,
                        behavior: 'smooth'
                    });
                }
            }
        });
    });
}

// Die Initialisierungsaufrufe (setupMembershipForm, setupDonationForm etc.)
// sollten weiterhin von deiner main.js -> initializeApp() erfolgen.

// reCAPTCHA-Callback-Funktion global verfügbar machen
window.recaptchaCallback = function() {
    console.log("reCAPTCHA wurde erfolgreich validiert");
    // Fehlermeldung ausblenden, wenn vorhanden
    const recaptchaErrors = document.querySelectorAll('[id$="-recaptcha-error"]');
    recaptchaErrors.forEach(error => error.classList.add('hidden'));
};

// reCAPTCHA-Fehler-Callback-Funktion
window.recaptchaErrorCallback = function() {
    console.log("reCAPTCHA-Validierung fehlgeschlagen");
};

// Funktion zum Anzeigen von Zahlungs-Feedback basierend auf URL-Parametern
function checkPaymentStatus() {
    // URL-Parameter abrufen
    const urlParams = new URLSearchParams(window.location.search);
    const paymentStatus = urlParams.get('payment');
    const warning = urlParams.get('warning');
    const reason = urlParams.get('reason');
    
    // Wenn kein Status vorhanden ist, nichts tun
    if (!paymentStatus) return;
    
    // Hash aus der URL entfernen, damit die Meldung nur einmal angezeigt wird
    const cleanUrl = window.location.pathname + window.location.hash;
    history.replaceState(null, '', cleanUrl);
    
    // Je nach Status entsprechende Meldung anzeigen
    switch(paymentStatus) {
        case 'success':
            Swal.fire({
                icon: 'success',
                title: i18nData[currentLanguage]?.payment_success_title || 'Vielen Dank für Ihre Spende!',
                text: i18nData[currentLanguage]?.payment_success_text || 'Ihre Zahlung wurde erfolgreich verarbeitet.',
                confirmButtonColor: '#4f46e5'
            });
            
            // Wenn es eine Warnung gibt, zusätzliche Info anzeigen
            if (warning === 'db') {
                console.warn('Datenbank-Warnung bei erfolgreicher Zahlung');
            }
            break;
            
        case 'cancelled':
            Swal.fire({
                icon: 'info',
                title: i18nData[currentLanguage]?.payment_cancelled_title || 'Zahlung abgebrochen',
                text: i18nData[currentLanguage]?.payment_cancelled_text || 'Sie haben den Zahlungsvorgang abgebrochen.',
                confirmButtonColor: '#6b7280'
            });
            break;
            
        case 'error':
            let errorText = i18nData[currentLanguage]?.payment_error_text || 'Bei der Verarbeitung Ihrer Zahlung ist ein Fehler aufgetreten.';
            
            // Spezifischere Fehlermeldung basierend auf dem Grund
            if (reason) {
                const reasonKey = `payment_error_${reason}`;
                const specificError = i18nData[currentLanguage]?.[reasonKey];
                if (specificError) {
                    errorText = specificError;
                }
            }
            
            Swal.fire({
                icon: 'error',
                title: i18nData[currentLanguage]?.payment_error_title || 'Fehler bei der Zahlung',
                text: errorText,
                confirmButtonColor: '#d33'
            });
            break;
    }
}

// Diese Funktion beim Laden der Seite aufrufen
document.addEventListener('DOMContentLoaded', function() {
    // Bestehende Initialisierungen...
    
    // Zahlungsstatus überprüfen
    checkPaymentStatus();
});

// --- Tab-Funktionalität für Formulare ---
function setupTabs() {
    const tabs = document.querySelectorAll('.tab-container .tab');
    const tabContents = document.querySelectorAll('.tab-content');
    const formSectionTitle = document.getElementById('form-section-title');
    
    // Funktion zum Aktivieren eines Tabs
    function activateTab(tabElement) {
        // Alle Tabs deaktivieren
        tabs.forEach(tab => tab.classList.remove('active'));
        
        // Alle Tab-Inhalte ausblenden
        tabContents.forEach(content => content.classList.remove('active'));
        
        // Angeklickten Tab aktivieren
        tabElement.classList.add('active');
        
        // Zugehörigen Tab-Inhalt anzeigen
        const tabId = tabElement.getAttribute('data-tab');
        const tabContent = document.getElementById(tabId + '-tab');
        if (tabContent) {
            tabContent.classList.add('active');
        }

                // Überschrift ändern je nach Tab
        if (formSectionTitle) {
            if (tabId === 'donation') {
                formSectionTitle.setAttribute('data-translate', 'donation_title');
                // Übersetzten Text direkt aus i18nData holen, statt nur Standardtext zu setzen
                if (typeof i18nData !== 'undefined' && i18nData[currentLanguage] && i18nData[currentLanguage]['donation_title']) {
                    formSectionTitle.textContent = i18nData[currentLanguage]['donation_title'];
                } else {
                    formSectionTitle.textContent = 'Spenden'; // Fallback, wenn Übersetzung nicht verfügbar
                }
            } else {
                formSectionTitle.setAttribute('data-translate', 'join_title');
                // Übersetzten Text direkt aus i18nData holen, statt nur Standardtext zu setzen
                if (typeof i18nData !== 'undefined' && i18nData[currentLanguage] && i18nData[currentLanguage]['join_title']) {
                    formSectionTitle.textContent = i18nData[currentLanguage]['join_title'];
                } else {
                    formSectionTitle.textContent = 'Mitglied werden'; // Fallback, wenn Übersetzung nicht verfügbar
                }
            }
        }
        
        // URL-Hash aktualisieren, ohne die Seite neu zu laden
        if (tabId === 'donation') {
            history.replaceState(null, null, '#spenden');
        } else if (tabId === 'membership') {
            history.replaceState(null, null, '#mitglied-werden');
        }
    }
    
    // Event-Listener für Tabs
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            activateTab(this);
            
            // URL-Hash aktualisieren, ohne die Seite neu zu laden
            const tabId = this.getAttribute('data-tab');
            if (tabId === 'donation') {
                history.replaceState(null, null, '#spenden');
            } else if (tabId === 'membership') {
                history.replaceState(null, null, '#mitglied-werden');
            }
        });
    });
    
    // Funktion zum Aktivieren eines Tabs über seinen data-tab-Wert
    window.activateTabByName = function(tabName) {
        const tab = document.querySelector(`.tab[data-tab="${tabName}"]`);
        if (tab) {
            activateTab(tab);
            return true;
        }
        return false;
    };
}
