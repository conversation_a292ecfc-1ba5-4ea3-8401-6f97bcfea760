
/* <PERSON><PERSON> bleiben hier... */
.bg-primary { background-color: #013476; }
.bg-secondary { background-color: #1CC5CC; }
.text-primary { color: #013476; }
.text-secondary { color: #1CC5CC; }
.border-primary { border-color: #013476; }
.border-secondary { border-color: #1CC5CC; }
.hover\:bg-primary:hover { background-color: #013476; }
.hover\:bg-secondary:hover { background-color: #1CC5CC; }

.dropdown:hover .dropdown-menu { display: block; }

.hero-image {
    background-image: linear-gradient(rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.7)), url('../assets/images/stadt_luenen.jpg'); /* Pfad angepasst */
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 400px;
    display: flex;
    align-items: center;
    position: relative;
    padding-bottom: 80px;
}
.hero-content { position: relative; z-index: 2; }

.form-checkbox:checked { background-color: #1CC5CC; border-color: #1CC5CC; }

.language-selector { position: relative; display: inline-block; }
.language-dropdown { display: none; position: absolute; right: 0; background-color: white; min-width: 160px; box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2); z-index: 100; border-radius: 4px; }
.language-selector:hover .language-dropdown { display: block; }

/* Für RTL-Sprachen (Arabisch) */
.rtl { direction: rtl; text-align: right; }
/* Anpassungen für RTL, z.B. bei Icons und Margins */
.rtl .language-dropdown { right: auto; left: 0; }
.rtl .fa-chevron-down { margin-left: 0; margin-right: 0.25rem; }
.rtl .fa-chevron-right { margin-left: 0; margin-right: 0.25rem; transform: rotate(180deg); } /* Beispiel für Pfeile */
.rtl .mr-1 { margin-right: 0; margin-left: 0.25rem; }
.rtl .ml-1 { margin-left: 0; margin-right: 0.25rem; }
.rtl .mr-2 { margin-right: 0; margin-left: 0.5rem; }
.rtl .ml-2 { margin-left: 0; margin-right: 0.5rem; }
.rtl .mr-3 { margin-right: 0; margin-left: 0.75rem; }
.rtl .ml-3 { margin-left: 0; margin-right: 0.75rem; }
.rtl .mr-4 { margin-right: 0; margin-left: 1rem; }
.rtl .ml-4 { margin-left: 0; margin-right: 1rem; }
.rtl .space-x-4 > :not([hidden]) ~ :not([hidden]) { margin-right: 1rem; margin-left: 0; }
.rtl .space-x-6 > :not([hidden]) ~ :not([hidden]) { margin-right: 1.5rem; margin-left: 0; }
.rtl .space-x-8 > :not([hidden]) ~ :not([hidden]) { margin-right: 2rem; margin-left: 0; }
/* Weitere RTL-Anpassungen nach Bedarf */


.dropdown-menu { display: none; position: absolute; background-color: white; min-width: 200px; box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2); z-index: 100; border-radius: 4px; top: 100%; left: 0; }
.dropdown:hover .dropdown-menu { display: block; }

.mobile-menu { display: none; }
.mobile-menu.active { display: block; }

/* Styles für Tabs */
.tab-container { display: flex; border-bottom: 1px solid #e5e7eb; margin-bottom: 1.5rem; }
.tab { padding: 0.75rem 1.5rem; cursor: pointer; font-weight: 500; border-bottom: 3px solid transparent; transition: all 0.3s ease; }
.tab.active { border-bottom-color: #1CC5CC; color: #013476; }
.tab-content { display: none; }
.tab-content.active { display: block; }

/* Styling für Validierungsfehler */
.input-error { border-color: #f56565 !important; /* Wichtig, um Tailwind zu überschreiben */ }
label.text-red-500 { color: #f56565 !important; }


/* Float-Menu CSS (falls du die style.min.css nicht lokal hast) */
/* Füge hier den Inhalt von style.min.css ein oder verlinke es korrekt */
/* @import url('./style.min.css'); */


@media (max-width: 768px) {
    .hero-image { background-position: center 30%; min-height: 350px; padding-bottom: 70px; }
    .dropdown-menu { position: static; box-shadow: none; width: 100%; }
    .rtl .dropdown-menu { text-align: right; } /* Anpassung für RTL im Mobile Menü */
}
@media (max-width: 480px) {
    .hero-image { min-height: 300px; padding-bottom: 60px; }
}

/* Line clamp für Instagram Modal */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Instagram Modal Anpassungen */
#instagram-modal-content {
    max-height: 90vh;
}

@media (max-width: 768px) {
    #modal-post-text {
        max-height: 4.5em; /* Ungefähr 2 Zeilen */
        transition: max-height 0.3s ease;
    }
    
    #modal-post-text.line-clamp-2 {
        overflow: hidden;
    }
    
    #modal-post-text:not(.line-clamp-2) {
        max-height: 300px;
        overflow-y: auto;
    }
}

/* Instagram Modal Text Styles */
#modal-post-text {
    transition: all 0.3s ease;
}

#modal-post-text.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    max-height: 4.5em; /* Ungefähr 2 Zeilen */
}

/* Auf Desktop keine Begrenzung */
@media (min-width: 768px) {
    #modal-post-text.line-clamp-2.md\:line-clamp-none {
        display: block;
        -webkit-line-clamp: unset;
        -webkit-box-orient: unset;
        overflow: visible;
        max-height: none;
    }
}

#modal-post-text.expanded-text {
    display: block;
    overflow: visible !important;
    max-height: none !important;
    height: auto !important;
}

/* Verhindern von Scrollbalken im Text auf mobilen Geräten */
@media (max-width: 768px) {
    #modal-post-text {
        overflow: visible !important;
        max-height: none !important;
    }
    
    #modal-post-text.line-clamp-2 {
        overflow: hidden !important;
        max-height: 4.5em !important;
    }
}

/* Sicherstellen, dass der gesamte Modal scrollbar ist, wenn nötig */
#instagram-post-modal .bg-white {
    max-height: 90vh;
    overflow-y: auto;
}

/* Sticky Header im Modal */
#instagram-post-modal .flex.justify-end.items-center {
    position: sticky;
    top: 0;
    background-color: white;
    z-index: 10;
}
