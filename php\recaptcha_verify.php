<?php
require_once __DIR__ . '/config.php';

/**
 * Überprüft die Google reCAPTCHA Antwort serverseitig.
 *
 * @param string $recaptchaResponse Der Wert von 'g-recaptcha-response' aus dem POST-Request.
 * @return bool True, wenn die Überprüfung erfolgreich war, sonst False.
 */
function verifyRecaptcha(string $recaptchaResponse): bool
{
    if (empty($recaptchaResponse)) {
        return false;
    }

    $verificationUrl = 'https://www.google.com/recaptcha/api/siteverify';
    $data = [
        'secret'   => RECAPTCHA_SECRET_KEY,
        'response' => $recaptchaResponse,
        'remoteip' => $_SERVER['REMOTE_ADDR'] ?? null // IP des Nutzers (optional aber empfohlen)
    ];

    $options = [
        'http' => [
            'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
            'method'  => 'POST',
            'content' => http_build_query($data),
            'timeout' => 5 // Timeout in Sekunden
        ]
    ];

    $context  = stream_context_create($options);
    $resultJson = @file_get_contents($verificationUrl, false, $context); // @ unterdrückt Fehler bei Timeout etc.

    if ($resultJson === false) {
        error_log("reCAPTCHA verification request failed.");
        return false; // Fehler bei der Anfrage an Google
    }

    $result = json_decode($resultJson);

    if ($result === null) {
        error_log("Failed to decode reCAPTCHA verification response: " . $resultJson);
        return false; // Fehler beim Dekodieren der Antwort
    }

    // Logge die Antwort von Google zum Debuggen (in Produktion ggf. entfernen oder anpassen)
    // error_log("reCAPTCHA response: " . $resultJson);

    return $result->success === true; // True nur, wenn Google 'success: true' zurückgibt
}
?>