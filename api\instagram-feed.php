<?php
// api/instagram-feed.php - Proxy für die Instagram API

// Fehlerbehandlung
ini_set('display_errors', 0);
error_reporting(0);

// Ausgabe als JSON
header('Content-Type: application/json');

// Konfiguration
require_once __DIR__ . '/../php/config.php';

// Instagram API Konfiguration
$instagram_token = INSTAGRAM_TOKEN; // Muss in config.php definiert sein

// Parameter prüfen
$username = isset($_GET['username']) ? $_GET['username'] : 'vision4luenen';
$count = isset($_GET['count']) ? intval($_GET['count']) : 6;

// Maximale Anzahl begrenzen
$count = min($count, 12);

// Cache-Datei
$cache_file = __DIR__ . '/../cache/instagram_' . $username . '.json';
$cache_time = 3600; // 1 Stunde Cache-Zeit

// Prüfen, ob C<PERSON> existiert und noch gültig ist
if (file_exists($cache_file) && (time() - filemtime($cache_file) < $cache_time)) {
    // Cache verwenden
    echo file_get_contents($cache_file);
    exit;
}

// Instagram API URL
$api_url = "https://graph.instagram.com/me/media?fields=id,caption,media_type,media_url,permalink,thumbnail_url,timestamp,username&access_token={$instagram_token}&limit={$count}";

// API-Anfrage senden
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

// Prüfen, ob die Anfrage erfolgreich war
if ($http_code !== 200) {
    // Fehler zurückgeben
    echo json_encode([
        'success' => false,
        'message' => 'Fehler beim Abrufen der Instagram-Daten',
        'posts' => []
    ]);
    exit;
}

// Daten verarbeiten
$data = json_decode($response, true);
$posts = [];

if (isset($data['data']) && is_array($data['data'])) {
    foreach ($data['data'] as $post) {
        // Nur Bilder und Videos berücksichtigen
        if ($post['media_type'] === 'IMAGE' || $post['media_type'] === 'VIDEO' || $post['media_type'] === 'CAROUSEL_ALBUM') {
            $posts[] = [
                'id' => $post['id'],
                'caption' => isset($post['caption']) ? $post['caption'] : '',
                'media_url' => $post['media_url'],
                'permalink' => $post['permalink'],
                'timestamp' => strtotime($post['timestamp']),
                'type' => $post['media_type'],
                'thumbnail_url' => isset($post['thumbnail_url']) ? $post['thumbnail_url'] : null
            ];
        }
    }
}

// Ergebnis zusammenstellen
$result = [
    'success' => true,
    'posts' => $posts
];

// Cache speichern
if (!is_dir(__DIR__ . '/../cache')) {
    mkdir(__DIR__ . '/../cache', 0755, true);
}
file_put_contents($cache_file, json_encode($result));

// Ausgabe
echo json_encode($result);
