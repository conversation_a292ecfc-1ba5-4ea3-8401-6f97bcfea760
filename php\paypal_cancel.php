<?php
require_once __DIR__ . '/config.php';
require_once BASE_PATH . '/php/db_connect.php';

// PayPal-Parameter aus der URL holen
$paymentId = $_GET['token'] ?? '';

if (!empty($paymentId)) {
    // Spende in der Datenbank als abgebrochen markieren
    try {
        $stmt = $pdo->prepare("
            UPDATE spender 
            SET 
                status = 'abgebrochen', 
                aktualisiert_am = NOW()
            WHERE 
                transaktions_id = :transaktions_id AND
                status = 'ausstehend'
        ");
        
        $stmt->execute([':transaktions_id' => $paymentId]);
    } catch (PDOException $e) {
        // Fehler ignorieren
    }
}

// Weiterleitung zur Spendenformular-Seite mit Abbruch-Parameter
header('Location: /?payment=cancelled#spenden');
