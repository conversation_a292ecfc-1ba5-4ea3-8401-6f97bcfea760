<?php
require_once __DIR__ . '/config.php';
require_once BASE_PATH . '/php/db_connect.php';

// PayPal-Parameter aus der URL holen
$paymentId = $_GET['token'] ?? '';
$payerId = $_GET['PayerID'] ?? '';

if (empty($paymentId)) {
    header('Location: /?payment=error#spenden');
    exit;
}

// PayPal API-Konfiguration
$paypalMode = PAYPAL_MODE;
$clientId = PAYPAL_CLIENT_ID;
$clientSecret = PAYPAL_CLIENT_SECRET;

// API-URLs basierend auf dem Modus
$apiUrl = ($paypalMode === 'sandbox') 
    ? 'https://api-m.sandbox.paypal.com' 
    : 'https://api-m.paypal.com';

// Access Token von PayPal holen
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl . '/v1/oauth2/token');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, 'grant_type=client_credentials');
curl_setopt($ch, CURLOPT_USERPWD, $clientId . ':' . $clientSecret);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);

$result = curl_exec($ch);
if (curl_errno($ch)) {
    header('Location: /?payment=error&reason=auth#spenden');
    exit;
}

$authResponse = json_decode($result, true);
curl_close($ch);

if (!isset($authResponse['access_token'])) {
    header('Location: /?payment=error&reason=auth#spenden');
    exit;
}

$accessToken = $authResponse['access_token'];

// Zahlung bei PayPal abschließen
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl . '/v2/checkout/orders/' . $paymentId . '/capture');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, '{}');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: Bearer ' . $accessToken
]);

$result = curl_exec($ch);
if (curl_errno($ch)) {
    header('Location: /?payment=error&reason=capture#spenden');
    exit;
}

$captureResponse = json_decode($result, true);
curl_close($ch);

if (!isset($captureResponse['status']) || $captureResponse['status'] !== 'COMPLETED') {
    header('Location: /?payment=error&reason=status#spenden');
    exit;
}

// Spende in der Datenbank aktualisieren
try {
    $stmt = $pdo->prepare("
        UPDATE spender 
        SET 
            status = 'abgeschlossen', 
            bezahlt_am = NOW(),
            aktualisiert_am = NOW()
        WHERE 
            transaktions_id = :transaktions_id AND
            status = 'ausstehend'
    ");
    
    $stmt->execute([':transaktions_id' => $paymentId]);
    
    // Weiterleitung zur Erfolgsseite
    header('Location: /?payment=success#spenden');
    
} catch (PDOException $e) {
    // Bei Datenbankfehler trotzdem zur Erfolgsseite weiterleiten, aber mit Warnung
    header('Location: /?payment=success&warning=db#spenden');
}

