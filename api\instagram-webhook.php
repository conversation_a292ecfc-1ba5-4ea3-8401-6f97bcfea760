<?php
// api/instagram-webhook.php - Webhook für Instagram-Benachrichtigungen

// Fehlerbehandlung
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Konfiguration
require_once __DIR__ . '/../php/config.php';

// Erweiterte Log-Funktion
function logWebhook($message, $level = 'INFO') {
    $logFile = __DIR__ . '/../logs/instagram_webhook.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $remoteIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    $requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'unknown';
    
    $logMessage = "[$timestamp][$level][$remoteIp][$requestMethod] $message | UA: $userAgent\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

// Anfang des Skripts loggen
logWebhook("Instagram Webhook aufgerufen", "START");

// Webhook-Verifizierung (GET-Anfrage)
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $mode = $_GET['hub_mode'] ?? '';
    $token = $_GET['hub_verify_token'] ?? '';
    $challenge = $_GET['hub_challenge'] ?? '';
    
    logWebhook("Verifizierungsanfrage erhalten: mode=$mode, token=$token, challenge=$challenge");
    
    // Verifizierungstoken überprüfen
    if ($mode === 'subscribe' && $token === INSTAGRAM_VERIFY_TOKEN) {
        logWebhook("Verifizierung erfolgreich", "SUCCESS");
        echo $challenge;
        exit;
    }
    
    // Ungültige Verifizierung
    logWebhook("Verifizierung fehlgeschlagen: Token stimmt nicht überein oder Mode ist nicht 'subscribe'", "ERROR");
    http_response_code(403);
    exit;
}

// Webhook-Benachrichtigung (POST-Anfrage)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Rohe POST-Daten lesen
    $input = file_get_contents('php://input');
    logWebhook("Webhook-Benachrichtigung erhalten: $input");
    
    // Daten verarbeiten
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        logWebhook("Fehler beim Parsen der JSON-Daten: " . json_last_error_msg(), "ERROR");
        http_response_code(400);
        exit;
    }
    
    // Detaillierte Informationen über die empfangenen Daten loggen
    logWebhook("Empfangene Daten: " . print_r($data, true), "DEBUG");
    
    // Cache löschen, um neue Beiträge zu laden
    $cacheFiles = glob(__DIR__ . '/../cache/instagram_*.json');
    if (empty($cacheFiles)) {
        logWebhook("Keine Cache-Dateien gefunden", "WARNING");
    } else {
        foreach ($cacheFiles as $file) {
            if (unlink($file)) {
                logWebhook("Cache-Datei gelöscht: $file", "SUCCESS");
            } else {
                logWebhook("Fehler beim Löschen der Cache-Datei: $file", "ERROR");
            }
        }
    }
    
    // Erfolgreiche Antwort
    logWebhook("Webhook-Verarbeitung abgeschlossen", "SUCCESS");
    http_response_code(200);
    echo json_encode(['success' => true]);
    exit;
}

// Andere Anfragemethoden ablehnen
logWebhook("Ungültige Anfragemethode: " . $_SERVER['REQUEST_METHOD'], "ERROR");
http_response_code(405);
echo json_encode(['error' => 'Method Not Allowed']);
