// operation-admin.js

// === GLOBALE VARIABLEN ===
let BASE_URL = ''; // Wird nach DOMContentLoaded gesetzt
let currentApplicationData = null;
let currentMemberData = null;

// --- Hilfsfunktionen ---
function showLoading(tableBodyId) {
    const tableBody = document.getElementById(tableBodyId);
    if (tableBody) {
        tableBody.innerHTML = '<tr><td colspan="10" class="text-center py-4">Lade Daten... <i class="fas fa-spinner fa-spin"></i></td></tr>';
    }
}

function showError(tableBodyId, message) {
    const tableBody = document.getElementById(tableBodyId);
    if (tableBody) {
        tableBody.innerHTML = `<tr><td colspan="10" class="text-center py-4 text-red-600">${message || 'Ein Fehler ist aufgetreten.'}</td></tr>`;
    }
}

function formatDate(dateString) {
    if (!dateString) return '-';
    try {
        const date = new Date(dateString.replace(' ', 'T') + 'Z');
        if (isNaN(date.getTime())) return dateString;
        return date.toLocaleDateString('de-DE', { year: 'numeric', month: '2-digit', day: '2-digit' });
    } catch (e) { return dateString; }
}

function formatInterval(interval) {
    const map = { 'monatlich': 'Monatlich', 'vierteljährlich': 'Vierteljährlich', 'halbjährlich': 'Halbjährlich', 'jährlich': 'Jährlich' };
    return map[interval] || interval || '-';
}

function formatStatus(status) {
     const map = {
        'eingegangen': { text: 'Eingegangen', class: 'status-pending' },
        'in Bearbeitung': { text: 'In Bearbeitung', class: 'status-pending' },
        'angenommen': { text: 'Angenommen', class: 'status-approved' },
        'abgelehnt': { text: 'Abgelehnt', class: 'status-rejected' },
        'zurückgezogen': { text: 'Zurückgezogen', class: 'status-rejected' },
        'aktiv': { text: 'Aktiv', class: 'status-approved' },
        'inaktiv': { text: 'Inaktiv', class: 'status-inactive' },
        'beitragsfrei': { text: 'Beitragsfrei', class: 'status-pending' },
        'ausgetreten': { text: 'Ausgetreten', class: 'status-rejected' },
        'verstorben': { text: 'Verstorben', class: 'status-inactive' }
    };
    const defaultStatus = { text: status || 'Unbekannt', class: 'status-inactive' };
    return map[status] || defaultStatus;
}

// --- Tab-Logik ---
function setupTabs() {
    const tabs = document.querySelectorAll('.tab');
    tabs.forEach(tab => {
// Innerhalb von setupTabs(), im click-Listener:
tab.addEventListener("click", () => {
  tabs.forEach((t) => t.classList.remove("active"));
  document
    .querySelectorAll(".tab-content")
    .forEach((c) => c.classList.remove("active"));

  tab.classList.add("active");
  const tabId = tab.getAttribute("data-tab");
  const tabContent = document.getElementById(`${tabId}-tab`); // Z.B. members-tab, payments-tab etc.
  if (tabContent) {
    tabContent.classList.add("active");
  }

  // === NEU: Prüfen, ob Detailansicht geschlossen werden soll ===
  const detailView = document.getElementById("application-detail-view");
  const applicationListView = document.getElementById(
    "membership-applications-list-view"
  );

  if (tabId !== "membership-applications") {
    // Wenn ein anderer Tab als "Mitgliedsanträge" geklickt wird:
    if (detailView) detailView.classList.add("hidden"); // Detailansicht immer verstecken
    if (applicationListView) applicationListView.classList.add("hidden"); // Auch die Liste verstecken (da der Tab nicht aktiv ist)
    currentApplicationData = null; // Ausgewählten Antrag zurücksetzen
  } else {
    // Wenn der "Mitgliedsanträge"-Tab geklickt wird:
    // Stelle sicher, dass die Listenansicht gezeigt wird,
    // falls die Detailansicht gerade aktiv war.
    hideApplicationDetailView(); // Ruft die Funktion auf, die Liste zeigt und Details versteckt
  }
  // === ENDE NEU ===

  // Load data when tab is activated
  // (Die Logik zum Laden der Daten für den jeweiligen Tab bleibt gleich)
  if (tabId === "membership-applications") {
    // loadApplications() wird jetzt indirekt durch hideApplicationDetailView oben ausgelöst ODER
    // wir können es hier nochmal explizit aufrufen, falls nötig.
    // Besser ist es in hideApplicationDetailView nicht aufzurufen, sondern hier:
    if (
      !document
        .getElementById("application-detail-view")
        ?.classList.contains("active")
    ) {
      loadApplications(); // Nur laden, wenn nicht gerade Details angezeigt werden
    }
  } else if (tabId === "members") {
    loadMembers();
  } else if (tabId === "payments") {
    loadPayments();
  }
});
    });
}

// --- Modal-Funktionen ---
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) modal.classList.remove('hidden');
    // else { console.error(`Modal with ID ${modalId} not found.`); }
}

function closeModal(modalId) {
     const modal = document.getElementById(modalId);
    if (modal) modal.classList.add('hidden');
    // else { console.error(`Modal with ID ${modalId} not found.`); }
}

// --- Logout (nicht mehr benötigt, da Link direkt in dashboard.php) ---
// function logout() { ... }

// --- Datenladefunktionen (verwenden jetzt die globale BASE_URL) ---
// --- Datenladefunktionen ---
async function loadApplications() {
    if (!BASE_URL) { console.error("BASE_URL not set in loadApplications"); return; }
    const tableBodyId = 'applications-table-body';
    const countElementId = 'applications-count';
    showLoading(tableBodyId);
    try {
        const apiUrl = `${BASE_URL}/php/api/get_applications.php`; // API muss ALLE benötigten Felder liefern
        const response = await fetch(apiUrl);
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        const result = await response.json();
        const tableBody = document.getElementById(tableBodyId);
        const countElement = document.getElementById(countElementId);
        if (!tableBody || !countElement) return;
        tableBody.innerHTML = '';
        if (result.success && Array.isArray(result.data)) {
             if (result.data.length === 0) {
                 tableBody.innerHTML = '<tr><td colspan="6" class="text-center py-4">Keine offenen Anträge gefunden.</td></tr>';
             } else {
                 result.data.forEach(app => {
                     const row = document.createElement('tr');
                     // === AKTUALISIERT: Ruft jetzt showApplicationDetailView auf ===
                     row.className = 'hover:bg-gray-50 cursor-pointer';
                     row.onclick = () => showApplicationDetailView(app); // Klick auf Zeile
                     const statusInfo = formatStatus(app.antrag_status);
                     row.innerHTML = `
                         <td class="px-6 py-4 whitespace-nowrap">${app.antrag_id}</td>
                         <td class="px-6 py-4 whitespace-nowrap">${app.vorname} ${app.nachname}</td>
                         <td class="px-6 py-4 whitespace-nowrap">${app.email}</td>
                         <td class="px-6 py-4 whitespace-nowrap">${formatDate(app.eingegangen_am)}</td>
                         <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 py-1 rounded-full text-xs font-medium ${statusInfo.class}">${statusInfo.text}</span></td>
                         <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <!-- === AKTUALISIERT: Button ruft auch showApplicationDetailView auf === -->
                             <button onclick='event.stopPropagation(); showApplicationDetailView(${JSON.stringify(app).replace(/'/g, "\\'")})' class="text-secondary hover:text-primary mr-3" aria-label="Details anzeigen"><i class="fas fa-eye"></i></button>
                         </td>`;
                     tableBody.appendChild(row);
                 });
             }
            countElement.textContent = `${result.data.length} Anträge gefunden`;
        } else {
            showError(tableBodyId, result.message);
            countElement.textContent = '0 Anträge gefunden';
        }
    } catch (error) {
        showError(tableBodyId, 'Netzwerkfehler oder Server nicht erreichbar.');
        const countElement = document.getElementById(countElementId);
        if(countElement) countElement.textContent = '0 Anträge gefunden';
    }
}

async function loadMembers() {
    if (!BASE_URL) { console.error("BASE_URL not set in loadMembers"); return; }
    const tableBodyId = 'members-table-body';
    const countElementId = 'members-count';
    showLoading(tableBodyId);
    try {
        const apiUrl = `${BASE_URL}/php/api/get_members.php`;
        const response = await fetch(apiUrl);
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        const result = await response.json();
        const tableBody = document.getElementById(tableBodyId);
        const countElement = document.getElementById(countElementId);
        if (!tableBody || !countElement) return;
        tableBody.innerHTML = '';
        if (result.success && Array.isArray(result.data)) {
             if (result.data.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="7" class="text-center py-4">Keine Mitglieder gefunden.</td></tr>';
            } else {
                 result.data.forEach(member => {
                     const row = document.createElement('tr');
                     row.className = 'hover:bg-gray-50 cursor-pointer';
                     row.onclick = () => showMemberDetails(member);
                     const statusInfo = formatStatus(member.mitglied_status);
                     row.innerHTML = `
                         <td class="px-6 py-4 whitespace-nowrap">${member.mitgliedsnummer || member.person_id}</td>
                         <td class="px-6 py-4 whitespace-nowrap">${member.vorname} ${member.nachname}</td>
                         <td class="px-6 py-4 whitespace-nowrap">${member.email}</td>
                         <td class="px-6 py-4 whitespace-nowrap">${formatInterval(member.aktuelles_zahlungsintervall)}</td>
                         <td class="px-6 py-4 whitespace-nowrap">${formatDate(member.letzte_zahlung)}</td>
                          <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 py-1 rounded-full text-xs font-medium ${statusInfo.class}">${statusInfo.text}</span></td>
                         <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button onclick='event.stopPropagation(); showMemberDetails(${JSON.stringify(member).replace(/'/g, "\\'")})' class="text-secondary hover:text-primary mr-3" aria-label="Details anzeigen"><i class="fas fa-eye"></i></button>
                         </td>`; // Sichereres Escaping
                     tableBody.appendChild(row);
                 });
             }
            countElement.textContent = `${result.data.length} Mitglieder gefunden`;
        } else {
            showError(tableBodyId, result.message);
            countElement.textContent = '0 Mitglieder gefunden';
        }
    } catch (error) {
        // console.error('Fetch error loadMembers:', error);
        showError(tableBodyId, 'Netzwerkfehler oder Server nicht erreichbar.');
        const countElement = document.getElementById(countElementId);
        if(countElement) countElement.textContent = '0 Mitglieder gefunden';
    }
}

async function loadPayments() {
    if (!BASE_URL) { console.error("BASE_URL not set in loadPayments"); return; }
    const tableBodyId = 'payments-table-body';
    const countElementId = 'payments-count';
    showLoading(tableBodyId);
    try {
        const apiUrl = `${BASE_URL}/php/api/get_payments.php`;
        const response = await fetch(apiUrl);
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        const result = await response.json();
        const tableBody = document.getElementById(tableBodyId);
        const countElement = document.getElementById(countElementId);
        if (!tableBody || !countElement) return;
        tableBody.innerHTML = '';
        if (result.success && Array.isArray(result.data)) {
             if (result.data.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="7" class="text-center py-4">Keine Beitragszahlungen gefunden.</td></tr>';
            } else {
                result.data.forEach(payment => {
                    const row = document.createElement('tr');
                    row.className = 'hover:bg-gray-50';
                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap">${payment.zahlung_id}</td>
                        <td class="px-6 py-4 whitespace-nowrap">${payment.vorname} ${payment.nachname}</td>
                        <td class="px-6 py-4 whitespace-nowrap">${payment.mitgliedsnummer || '-'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-right">${parseFloat(payment.betrag).toFixed(2)} €</td>
                        <td class="px-6 py-4 whitespace-nowrap">${formatDate(payment.zahlungsdatum)}</td>
                        <td class="px-6 py-4 whitespace-nowrap">${payment.zeitraum_bezahlt_fuer || '-'}</td>
                        <td class="px-6 py-4 whitespace-nowrap">${payment.zahlungsart || '-'}</td>
                    `;
                    tableBody.appendChild(row);
                });
            }
            countElement.textContent = `${result.data.length} Zahlungen gefunden`;
        } else {
            showError(tableBodyId, result.message);
            countElement.textContent = '0 Zahlungen gefunden';
        }
    } catch (error) {
        // console.error('Fetch error loadPayments:', error);
        showError(tableBodyId, 'Netzwerkfehler oder Server nicht erreichbar.');
         const countElement = document.getElementById(countElementId);
        if(countElement) countElement.textContent = '0 Zahlungen gefunden';
    }
}

// --- NEU: Funktionen zum Anzeigen/Verstecken der Detailansicht ---
function showApplicationDetailView(application) {
    console.log("Showing details for application:", application);
    currentApplicationData = application; // Speichere die Daten global

    // Daten in die Detailansicht füllen
    document.getElementById('detail-antrag-id').textContent = application.antrag_id ?? '-';
    const statusInfo = formatStatus(application.antrag_status);
    const statusSpan = document.getElementById('detail-antrag-status');
    statusSpan.textContent = statusInfo.text;
    statusSpan.className = `ml-1 px-2 py-0.5 rounded-full text-xs font-medium ${statusInfo.class}`; // Klassen aktualisieren

    // Allgemeine Infos
    document.getElementById('detail-vorname').textContent = application.vorname ?? '-';
    document.getElementById('detail-nachname').textContent = application.nachname ?? '-';
    document.getElementById('detail-geburtsdatum').textContent = formatDate(application.geburtsdatum);
    document.getElementById('detail-organisation').textContent = application.organisation || '-';

    // Adresse
    document.getElementById('detail-strasse').textContent = application.strasse_hausnummer ?? '-';
    document.getElementById('detail-plz').textContent = application.plz ?? '-';
    document.getElementById('detail-ort').textContent = application.ort ?? '-';
    document.getElementById('detail-land').textContent = application.land ?? '-';

    // Kontakt
    document.getElementById('detail-telefon').textContent = application.telefon || '-';
    document.getElementById('detail-email').textContent = application.email ?? '-';

    // Mitgliedschaft
    document.getElementById('detail-art').textContent = application.art_der_mitgliedschaft ?? 'Fördermitglied';
    document.getElementById('detail-intervall').textContent = formatInterval(application.gewuenschtes_zahlungsintervall);
    document.getElementById('detail-beitrag').textContent = `${parseFloat(application.beitrag || 0).toFixed(2)} €`;
    document.getElementById('detail-student').textContent = application.ist_student ? 'Ja' : 'Nein';

    // Weitere Infos
    document.getElementById('detail-datenschutz').textContent = application.datenschutz_akzeptiert ? 'Ja' : 'Nein';
    document.getElementById('detail-richtigkeit').textContent = application.bestaetigung_richtigkeit ? 'Ja' : 'Nein';
    document.getElementById('detail-keinepartei').textContent = application.keine_andere_partei ? 'Ja' : 'Nein';
    document.getElementById('detail-eingegangen').textContent = formatDate(application.eingegangen_am);
    document.getElementById('detail-bearbeitet').textContent = formatDate(application.bearbeitet_am);
    document.getElementById('detail-archiviert').textContent = application.ist_archiviert ? 'Ja' : 'Nein';
    document.getElementById('detail-archiviert-am').textContent = formatDate(application.archiviert_am);


    const detailView = document.getElementById('application-detail-view');
    const listView = document.getElementById('membership-applications-list-view');

    if (!detailView || !listView) {
        console.error("Detail view or list view container not found!");
        return;
    }

    // === Ansichten umschalten (Klassen anpassen) ===
    listView.classList.remove('active'); // Entferne active von Liste
    listView.classList.add('hidden');    // Verstecke Liste
    detailView.classList.remove('hidden'); // Zeige Details
    detailView.classList.add('active');    // Setze Details aktiv

    // Nach oben scrollen (sofort)
    window.scrollTo({ top: 0, behavior: 'auto' });
}

function hideApplicationDetailView() {
    console.log("Hiding application detail view.");
    const detailView = document.getElementById('application-detail-view');
    const listView = document.getElementById('membership-applications-list-view');

    // === Ansichten umschalten (Klassen anpassen) ===
    if (detailView) {
        detailView.classList.add('hidden');   // Verstecke Details
        detailView.classList.remove('active'); // Entferne active von Details
    }
    if (listView) {
        listView.classList.remove('hidden'); // Zeige Liste
        listView.classList.add('active');    // Setze Liste aktiv
    }

    currentApplicationData = null; // Daten zurücksetzen
}


function showMemberDetails(member) {
    currentMemberData = member;
    const detailsDiv = document.getElementById('member-details');
    if (!detailsDiv) return;
    // Hier nur ein Beispiel, passe es an die Daten an, die du wirklich von der API bekommst
    detailsDiv.innerHTML = `
         <div class="grid grid-cols-2 gap-4">
             <div><p class="text-sm text-gray-500">Mitgliedsnummer</p><p class="font-medium">${member.mitgliedsnummer || member.person_id}</p></div>
             <div><p class="text-sm text-gray-500">Status</p><p class="font-medium"><span class="px-2 py-1 rounded-full text-xs ${formatStatus(member.mitglied_status).class}">${formatStatus(member.mitglied_status).text}</span></p></div>
            <div><p class="text-sm text-gray-500">Vorname</p><p class="font-medium">${member.vorname ?? '-'}</p></div>
            <div><p class="text-sm text-gray-500">Nachname</p><p class="font-medium">${member.nachname ?? '-'}</p></div>
            <div><p class="text-sm text-gray-500">E-Mail</p><p class="font-medium">${member.email ?? '-'}</p></div>
            <div><p class="text-sm text-gray-500">Mitglied seit</p><p class="font-medium">${formatDate(member.mitglied_seit)}</p></div>
            <div><p class="text-sm text-gray-500">Zahlungsintervall</p><p class="font-medium">${formatInterval(member.aktuelles_zahlungsintervall)}</p></div>
            <div><p class="text-sm text-gray-500">Akt. Beitrag</p><p class="font-medium">${parseFloat(member.aktueller_beitrag_pro_monat || 0).toFixed(2)} €</p></div>
            <div><p class="text-sm text-gray-500">Letzte Zahlung</p><p class="font-medium">${formatDate(member.letzte_zahlung)}</p></div>
            <div><p class="text-sm text-gray-500">Student?</p><p class="font-medium">${member.ist_student ? 'Ja' : 'Nein'}</p></div>
             <!-- Weitere Details (Adresse etc. könnten von Personen-Tabelle kommen) -->
        </div>`;
    openModal('member-modal');
}

// --- Modal/Detail Aktionen ---
async function apiRequest(endpoint, data) {
    // Diese Funktion bleibt unverändert (wie in deiner letzten Version)
    if (!BASE_URL) { alert("Konfigurationsfehler: Basis-URL fehlt."); return null; }
    try {
        const apiUrl = `${BASE_URL}/php/api/${endpoint}`;
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify(data),
        });
        const responseData = await response.json();
        if (!response.ok) {
             throw new Error(responseData.message || `HTTP error! status: ${response.status}`);
        }
        return responseData;
    } catch (error) {
        console.error(`API request to ${endpoint} failed:`, error);
        // Zeige den Fehler dem User, aber gib null zurück, damit der Aufrufer weiß, dass es fehlgeschlagen ist
        Swal.fire('Fehler', `Aktion fehlgeschlagen: ${error.message}`, 'error'); // Hier ist Swal OK
        return null;
    }
}

// === ANGEPASSTE Funktion ===
async function approveCurrentApplication() {
    if (!currentApplicationData) {
        alert("Kein Antrag ausgewählt.");
        return;
    }
    console.log(`Approving application ${currentApplicationData.antrag_id}`);
    const result = await apiRequest('approve_application.php', { antrag_id: currentApplicationData.antrag_id });

    if (result && result.success) {
        // Erfolgreich! Lade die Daten für DIESEN Antrag neu, um den neuen Status zu sehen
        // Dazu brauchen wir einen neuen API-Endpunkt oder passen den bestehenden an.
        // Annahme: Wir haben einen Endpunkt get_application_details.php
        try {
            const detailApiUrl = `${BASE_URL}/php/api/get_application_details.php?id=${currentApplicationData.antrag_id}`; // Beispiel-Endpunkt
            const detailResponse = await fetch(detailApiUrl);
            if (!detailResponse.ok) throw new Error(`HTTP error! status: ${detailResponse.status}`);
            const detailResult = await detailResponse.json();

            if (detailResult.success && detailResult.data) {
                // Aktualisiere die Detailansicht mit den neuesten Daten
                showApplicationDetailView(detailResult.data);
                 // Optional: Kurze Erfolgsmeldung ohne Swal
                 const statusSpan = document.getElementById('detail-antrag-status');
                 if(statusSpan) {
                    statusSpan.insertAdjacentHTML('afterend', '<span class="text-green-600 text-xs ml-2 fade-out">(Genehmigt!)</span>');
                    setTimeout(()=> document.querySelector('.fade-out')?.remove(), 2000); // Entfernt nach 2 Sek.
                 }
            } else {
                // Fallback: Zeige Erfolgsmeldung und lade die Liste neu
                Swal.fire('Genehmigt!', result.message || 'Antrag erfolgreich genehmigt.', 'success');
                hideApplicationDetailView(); // Zurück zur Liste
                loadApplications();
            }
        } catch(error) {
            console.error("Error fetching updated application details:", error);
             Swal.fire('Genehmigt!', result.message || 'Antrag erfolgreich genehmigt, aber Details konnten nicht neu geladen werden.', 'warning');
             hideApplicationDetailView();
             loadApplications();
        }
        loadMembers(); // Mitgliederliste aktualisieren
    }
    // Fehlerfall wird bereits von apiRequest mit Swal behandelt
}

// === ANGEPASSTE Funktion ===
async function rejectCurrentApplication() {
     if (!currentApplicationData) {
        alert("Kein Antrag ausgewählt.");
        return;
    }
     console.log(`Rejecting application ${currentApplicationData.antrag_id}`);
     const result = await apiRequest('reject_application.php', { antrag_id: currentApplicationData.antrag_id });

      if (result && result.success) {
        // Erfolgreich! Lade die Daten für DIESEN Antrag neu
         try {
            const detailApiUrl = `${BASE_URL}/php/api/get_application_details.php?id=${currentApplicationData.antrag_id}`; // Beispiel-Endpunkt
            const detailResponse = await fetch(detailApiUrl);
            if (!detailResponse.ok) throw new Error(`HTTP error! status: ${detailResponse.status}`);
            const detailResult = await detailResponse.json();

            if (detailResult.success && detailResult.data) {
                // Aktualisiere die Detailansicht mit den neuesten Daten
                showApplicationDetailView(detailResult.data);
                 // Optional: Kurze Erfolgsmeldung ohne Swal
                 const statusSpan = document.getElementById('detail-antrag-status');
                 if(statusSpan) {
                    statusSpan.insertAdjacentHTML('afterend', '<span class="text-red-600 text-xs ml-2 fade-out">(Abgelehnt!)</span>');
                    setTimeout(()=> document.querySelector('.fade-out')?.remove(), 2000); // Entfernt nach 2 Sek.
                 }
            } else {
                // Fallback: Zeige Erfolgsmeldung und lade die Liste neu
                Swal.fire('Abgelehnt!', result.message || 'Antrag abgelehnt.', 'success');
                 hideApplicationDetailView(); // Zurück zur Liste
                 loadApplications();
            }
        } catch(error) {
            console.error("Error fetching updated application details:", error);
             Swal.fire('Abgelehnt!', result.message || 'Antrag abgelehnt, aber Details konnten nicht neu geladen werden.', 'warning');
             hideApplicationDetailView();
             loadApplications();
        }
    }
     // Fehlerfall wird bereits von apiRequest mit Swal behandelt
}

async function deleteMember() {
    if (!currentMemberData) return;
    const confirmation = await Swal.fire({
        title: 'Sind Sie sicher?',
        text: `Mitglied ${currentMemberData.vorname} ${currentMemberData.nachname} wirklich löschen? Dies kann nicht rückgängig gemacht werden!`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ja, löschen!',
        cancelButtonText: 'Abbrechen'
      });

    if (confirmation.isConfirmed) {
        const result = await apiRequest('delete_member.php', { person_id: currentMemberData.person_id });
         if (result && result.success) {
            Swal.fire('Gelöscht!', 'Das Mitglied wurde erfolgreich gelöscht.', 'success');
            closeModal('member-modal');
            loadMembers();
        } // Fehler wird von apiRequest behandelt
    }
}

function editMember() {
     if (!currentMemberData) return;
    // TODO: Implementiere Bearbeitungslogik
    Swal.fire('Platzhalter', `Mitglied ${currentMemberData.person_id} bearbeiten (Funktion noch nicht implementiert)`, 'info');
}

// --- Formular Handler ---
function setupAdminForms() {
    // Quittung erstellen
    const receiptForm = document.getElementById('receipt-form');
    if (receiptForm) {
        receiptForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            const data = Object.fromEntries(formData.entries());
            // TODO: API Call
            // const result = await apiRequest('create_receipt.php', data);
            Swal.fire('Gesendet (simuliert)', 'Quittungsdaten erhalten.', 'info');
            this.reset();
            const dateInput = document.getElementById('receipt-date');
            if(dateInput) dateInput.value = new Date().toISOString().split('T')[0];
        });
    }

    // Mitglied erstellen
    const membershipFormAdmin = document.getElementById('admin-membership-form');
    if (membershipFormAdmin) {
        membershipFormAdmin.addEventListener('submit', async function(e) {
            e.preventDefault();
             const formData = new FormData(this);
             const data = Object.fromEntries(formData.entries());
             // TODO: API Call
             // const result = await apiRequest('create_member.php', data);
             Swal.fire('Gesendet (simuliert)', 'Mitgliedsdaten erhalten.', 'info');
             this.reset();
             loadMembers();
        });
    }
}


// --- Initialisierung ---
document.addEventListener('DOMContentLoaded', () => {
    // BASE_URL aus dem body-Tag lesen
    const bodyElement = document.body;
    if (bodyElement && bodyElement.dataset.baseUrl) {
        BASE_URL = bodyElement.dataset.baseUrl.replace(/\/$/, ""); // Setze die globale Variable
        // console.log("Admin DOM Loaded. Base URL set to:", BASE_URL);
    } else {
        console.error("FATAL: Could not read base URL from body data-base-url attribute!");
        showError('applications-table-body', 'Konfigurationsfehler: Basis-URL fehlt.');
        showError('members-table-body', 'Konfigurationsfehler: Basis-URL fehlt.');
        showError('payments-table-body', 'Konfigurationsfehler: Basis-URL fehlt.');
        return; // Stoppe weitere Initialisierung
    }

    // Setup und initiales Laden
    setupTabs();
    setupAdminForms();
    loadApplications(); // Lade Anträge standardmäßig

    // Standarddatum für Quittung
    const today = new Date().toISOString().split('T')[0];
    const dateInput = document.getElementById('receipt-date');
     if (dateInput) {
        dateInput.value = today;
    }
});

// Füge hier alle onclick-Handler hinzu, die im HTML verwendet werden,
// damit sie global verfügbar sind oder ersetze sie durch Event Listener.
// Beispiel: Wenn refreshApplications im HTML als onclick verwendet wird:
function refreshApplications() {
    loadApplications();
}
function refreshMembers() {
    loadMembers();
}
function loadPaymentsTrigger() { // Umbenannt, falls loadPayments schon existiert
    loadPayments();
}
function exportMembers() {
    // TODO: Implement export logic or API call
    alert("Export Funktion noch nicht implementiert.");
}
function exportPayments() {
     // TODO: Implement export logic or API call
     alert("Export Funktion noch nicht implementiert.");
}
// Die Funktionen für Modals und Aktionen (approve, reject, delete, edit) sind oben schon definiert.