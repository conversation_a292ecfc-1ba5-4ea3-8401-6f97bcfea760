// js/main.js

// Globale Variablen
let isAppInitialized = false;
let lastScrollTargetId = null;
let lastScrollPosition = 0;

// --- Kernfunktionen zum Umschalten der Inhalte ---

function showSection(sectionId) {
    const mainContent = document.getElementById('main-content');
    const privacyContent = document.getElementById('privacy-policy-content');
    const impressumContent = document.getElementById('impressum-content');
    const statuteContent = document.getElementById('statute-content');
    const targetSection = document.getElementById(sectionId);
    const pageContent = document.getElementById('page-content');

    if (targetSection) {
        lastScrollPosition = window.pageYOffset || document.documentElement.scrollTop;
    }

    if (mainContent) mainContent.classList.add('hidden');
    if (privacyContent) privacyContent.classList.add('hidden');
    if (impressumContent) impressumContent.classList.add('hidden');
    if (statuteContent) statuteContent.classList.add('hidden');

    if (targetSection) {
        targetSection.classList.remove('hidden');
        if (pageContent) pageContent.classList.remove('hidden');
        window.scrollTo({ top: 0, behavior: 'auto' });
    } else {
        showMainContent(true); // Zeige Hauptinhalt und springe zurück als Fallback
    }
}

function showMainContent(shouldReturnToLastPosition = false) {
    const mainContent = document.getElementById('main-content');
    const privacyContent = document.getElementById('privacy-policy-content');
    const impressumContent = document.getElementById('impressum-content');
    const statuteContent = document.getElementById('statute-content');
    const pageContent = document.getElementById('page-content');

    if (privacyContent) privacyContent.classList.add('hidden');
    if (impressumContent) impressumContent.classList.add('hidden');
    if (statuteContent) statuteContent.classList.add('hidden');

    if (mainContent) {
        mainContent.classList.remove('hidden');
    }
    if (pageContent) pageContent.classList.remove('hidden');

    // Aktualisiere die URL, um den Hash zu entfernen
    if (window.location.hash === '#datenschutz' || 
        window.location.hash === '#impressum' || 
        window.location.hash === '#satzung') {
        history.pushState("", document.title, window.location.pathname + window.location.search);
    }

    if (shouldReturnToLastPosition) {
        let returned = false;
        if (lastScrollTargetId) {
            const targetElement = document.querySelector(lastScrollTargetId);
            if (targetElement) {
                 setTimeout(() => {
                    try {
                        const header = document.querySelector('header.sticky') || document.querySelector('header');
                        const headerOffset = header ? header.offsetHeight : 80;
                        const elementPosition = targetElement.getBoundingClientRect().top;
                        const offsetPosition = elementPosition + window.pageYOffset - headerOffset;
                        window.scrollTo({ top: offsetPosition, behavior: 'auto' });
                        returned = true;
                    } catch (e) {
                        returned = false;
                    } finally {
                        lastScrollTargetId = null;
                        lastScrollPosition = 0;
                    }
                 }, 50);
            } else {
                 lastScrollTargetId = null;
            }
        }
        setTimeout(() => {
            if (!returned && lastScrollPosition > 0) {
                window.scrollTo({ top: lastScrollPosition, behavior: 'auto' });
                lastScrollPosition = 0;
                lastScrollTargetId = null;
            } else if (!returned) {
                 lastScrollPosition = 0;
                 lastScrollTargetId = null;
            }
        }, 60);
    }
}

// Funktion für den Schließen-Button
function closeDynamicSectionAndReturn() {
    showMainContent(true);
}

// --- NEUE Struktur für Event Handling ---

// Handler für Klicks auf dynamische Inhalts-Links
function handleDelegatedDynamicLinkClick(event) {
    const link = event.target.closest('.dynamic-content-link');
    if (link) {
        event.preventDefault();
        event.stopPropagation();

        const targetId = link.getAttribute('data-target');

        const parentSection = link.closest('section[id]');
        if (parentSection && parentSection.id && parentSection.id !== 'page-content') {
            lastScrollTargetId = `#${parentSection.id}`;
        } else {
            lastScrollTargetId = null;
        }

        if (targetId) {
            showSection(targetId);
        }
    }
}

// Handler für Klicks auf Scroll-Links
function handleDelegatedScrollLinkClick(event) {
    const link = event.target.closest('.scroll-link');
    if (link) {
        const targetId = link.getAttribute('href');

        if (targetId && targetId.startsWith('#')) {
            event.preventDefault();
            event.stopPropagation();

            // Spezialfall für Wahlprogramm
            if (targetId === '#wahlprogramm') {
                console.log("Öffne Wahlprogramm PDF");
                window.open(window.location.origin + '/assets/docs/Wahlprogramm VISION 2025.pdf', '_blank');
                return;
            }

            if (targetId === '#datenschutz' || targetId === '#impressum' || targetId === '#satzung') {
                const sectionId = targetId === '#datenschutz' ? 'privacy-policy-content' : 
                                 (targetId === '#impressum' ? 'impressum-content' : 'statute-content');
                lastScrollTargetId = null;
                showSection(sectionId);
            } else if (targetId === '#spenden' || targetId === '#mitglied-werden') {
                // Spezieller Fall für Formular-Tabs
                const formSection = document.getElementById('mitglied-werden');
                
                // Tab aktivieren basierend auf dem Link
                if (targetId === '#spenden') {
                    const donationTab = document.querySelector('.tab[data-tab="donation"]');
                    if (donationTab) donationTab.click();
                } else {
                    const membershipTab = document.querySelector('.tab[data-tab="membership"]');
                    if (membershipTab) membershipTab.click();
                }
                
                // Zum Formular scrollen
                if (formSection) {
                    setTimeout(() => {
                        const header = document.querySelector('header.sticky') || document.querySelector('header');
                        const headerOffset = header ? header.offsetHeight : 80;
                        const elementPosition = formSection.getBoundingClientRect().top;
                        const offsetPosition = elementPosition + window.pageYOffset - headerOffset;
                        
                        window.scrollTo({
                            top: offsetPosition,
                            behavior: "smooth"
                        });
                    }, 100);
                }
            } else {
                lastScrollTargetId = targetId;
                showMainContent();

                setTimeout(() => {
                    const targetElement = document.querySelector(targetId);
                    if (targetElement) {
                        const header = document.querySelector('header.sticky') || document.querySelector('header');
                        const headerOffset = header ? header.offsetHeight : 80;
                        const elementPosition = targetElement.getBoundingClientRect().top;
                        const offsetPosition = elementPosition + window.pageYOffset - headerOffset;
                        window.scrollTo({
                            top: offsetPosition,
                            behavior: "smooth"
                        });
                    } else {
                        lastScrollTargetId = null;
                    }
                }, 50);
            }
        }
    }
}

// Handler für Klicks auf Schließen-Buttons
function handleDelegatedCloseButtonClick(event) {
    if (event.target.matches('.close-dynamic-section-button')) {
        closeDynamicSectionAndReturn();
    }
}

// --- Event Listener Setup ---
function setupEventListeners() {
    // Delegierte Event-Listener für verschiedene Interaktionen
    document.addEventListener('click', handleDelegatedDynamicLinkClick);
    document.addEventListener('click', handleDelegatedScrollLinkClick);
    document.addEventListener('click', handleDelegatedCloseButtonClick);
    
    // Weitere Event-Listener hier...
}

// --- Hauptinitialisierung ---

async function initializeApp() {
    if (isAppInitialized) {
        return;
    }

    // Sprache initialisieren...
    try {
        // Wenn serverLanguage definiert ist (von PHP gesetzt), diese verwenden
        if (typeof serverLanguage !== 'undefined' && i18nData[serverLanguage]) {
            currentLanguage = serverLanguage;
            // Auch im localStorage speichern
            try {
                localStorage.setItem('preferredLanguage', serverLanguage);
            } catch(e) {
                // Fehler beim Speichern im localStorage ignorieren
            }
        } else {
            // Fallback auf localStorage oder Browser-Sprache
            const preferredLanguage = localStorage.getItem('preferredLanguage');
            if (preferredLanguage && i18nData[preferredLanguage]) {
                currentLanguage = preferredLanguage;
            } else {
                // Standardsprache ist Deutsch, Browser-Sprache nur als Fallback
                const browserLang = navigator.language.split('-')[0];
                currentLanguage = (i18nData[browserLang]) ? browserLang : 'de';
            }
        }
        
        if (typeof updatePageLanguage === 'function') {
            updatePageLanguage();
        }
    } catch (e) { 
        console.error("Error initializing language:", e);
    }

    // UI-Komponenten initialisieren
    try {
        if (typeof setupLanguageSwitcher === 'function') setupLanguageSwitcher();
        if (typeof setupTabs === 'function') setupTabs();
        if (typeof setupMobileMenu === 'function') setupMobileMenu();
        if (typeof setupMembershipForm === 'function') setupMembershipForm();
        if (typeof setupDonationForm === 'function') setupDonationForm();
    } catch(e) { /* Fehler bei Setup ignorieren */ }

    // Zahlungs-Feedback überprüfen
    checkPaymentStatus();

    // Delegierte Event Listener für Links und Buttons einrichten
    setupEventListeners();

    // Initialen Zustand herstellen - nur wenn kein Hash verarbeitet wurde
    if (!processUrlHash()) {
        showMainContent();
    }

    isAppInitialized = true;
}

// Startpunkt: DOM laden, Übersetzungen laden, App initialisieren
document.addEventListener('DOMContentLoaded', async () => {
    if (typeof loadTranslations === 'function') {
        try {
            await loadTranslations();
        } catch (e) { /* Fehler beim Laden der Übersetzungen ignorieren? */ }
    }
    // initializeApp aufrufen (evtl. mit kleiner Verzögerung)
    setTimeout(initializeApp, 150);
});

// Funktion zum Überprüfen und Verarbeiten des URL-Hash
function processUrlHash() {
    const hash = window.location.hash;
    
    // Wenn die URL #wahlprogramm enthält, PDF öffnen
    if (hash === '#wahlprogramm') {
        console.log("Öffne Wahlprogramm PDF");
        // Füge eine Flag hinzu, um doppelte Öffnungen zu vermeiden
        if (!window.wahlprogrammOpened) {
            window.wahlprogrammOpened = true;
            window.open(window.location.origin + '/assets/docs/Wahlprogramm VISION 2025.pdf', '_blank');
            // Nach kurzer Zeit zurücksetzen, damit es bei erneutem Klick wieder funktioniert
            setTimeout(() => { window.wahlprogrammOpened = false; }, 1000);
        }
        return true;
    }
    
    if (hash === '#datenschutz') {
        console.log("Zeige Datenschutz");
        const mainContent = document.getElementById('main-content');
        const privacyContent = document.getElementById('privacy-policy-content');
        const impressumContent = document.getElementById('impressum-content');
        const statuteContent = document.getElementById('statute-content');
        const pageContent = document.getElementById('page-content');
        
        if (mainContent) mainContent.classList.add('hidden');
        if (impressumContent) impressumContent.classList.add('hidden');
        if (statuteContent) statuteContent.classList.add('hidden');
        if (privacyContent) {
            privacyContent.classList.remove('hidden');
            if (pageContent) pageContent.classList.remove('hidden');
        }
        return true;
    } 
    else if (hash === '#impressum') {
        console.log("Zeige Impressum");
        const mainContent = document.getElementById('main-content');
        const privacyContent = document.getElementById('privacy-policy-content');
        const impressumContent = document.getElementById('impressum-content');
        const statuteContent = document.getElementById('statute-content');
        const pageContent = document.getElementById('page-content');
        
        if (mainContent) mainContent.classList.add('hidden');
        if (privacyContent) privacyContent.classList.add('hidden');
        if (statuteContent) statuteContent.classList.add('hidden');
        if (impressumContent) {
            impressumContent.classList.remove('hidden');
            if (pageContent) pageContent.classList.remove('hidden');
        }
        return true;
    }
    else if (hash === '#satzung') {
        console.log("Zeige Satzung");
        const mainContent = document.getElementById('main-content');
        const privacyContent = document.getElementById('privacy-policy-content');
        const impressumContent = document.getElementById('impressum-content');
        const statuteContent = document.getElementById('statute-content');
        const pageContent = document.getElementById('page-content');
        
        if (mainContent) mainContent.classList.add('hidden');
        if (privacyContent) privacyContent.classList.add('hidden');
        if (impressumContent) impressumContent.classList.add('hidden');
        if (statuteContent) {
            statuteContent.classList.remove('hidden');
            if (pageContent) pageContent.classList.remove('hidden');
        }
        return true;
    }
    else if (hash === '#spenden' || hash === '#mitglied-werden') {
        console.log("Zeige Formular-Tab:", hash);
        
        // Hauptinhalt anzeigen (falls wir von Datenschutz/Impressum kommen)
        showMainContent();
        
        // Zum Formular-Bereich scrollen
        const formSection = document.getElementById('mitglied-werden');
        if (formSection) {
            setTimeout(() => {
                const header = document.querySelector('header.sticky') || document.querySelector('header');
                const headerOffset = header ? header.offsetHeight + 20 : 100;
                const elementPosition = formSection.getBoundingClientRect().top;
                const offsetPosition = elementPosition + window.pageYOffset - headerOffset;
                
                window.scrollTo({
                    top: offsetPosition,
                    behavior: 'smooth'
                });
                
                // Tab aktivieren
                if (hash === '#spenden') {
                    // Spenden-Tab aktivieren
                    const donationTab = document.querySelector('.tab[data-tab="donation"]');
                    if (donationTab) {
                        console.log("Aktiviere Spenden-Tab");
                        donationTab.click();
                    }
                } else {
                    // Mitgliedschaft-Tab aktivieren (ist normalerweise bereits aktiv)
                    const membershipTab = document.querySelector('.tab[data-tab="membership"]');
                    if (membershipTab) {
                        console.log("Aktiviere Mitgliedschaft-Tab");
                        membershipTab.click();
                    }
                }
            }, 100);
        }
        return true;
    }
    
    return false;
}

// Auch bei Hash-Änderungen den Hash verarbeiten
window.addEventListener('hashchange', function() {
    setTimeout(processUrlHash, 100);
});

// Sofort ausführen, sobald das Skript geladen ist
(function() {
    // Warten, bis das DOM geladen ist
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(processUrlHash, 100); // Kurze Verzögerung, um sicherzustellen, dass alle DOM-Elemente geladen sind
        });
    } else {
        // DOM ist bereits geladen
        setTimeout(processUrlHash, 100);
    }
})();
