// Cookie-Banner Funktionalität
document.addEventListener('DOMContentLoaded', function() {
    // Cookie-Funktionen
    function setCookie(name, value, days) {
        let expires = '';
        if (days) {
            const date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = '; expires=' + date.toUTCString();
        }
        document.cookie = name + '=' + (value || '') + expires + '; path=/; SameSite=Lax';
    }

    function getCookie(name) {
        const nameEQ = name + '=';
        const ca = document.cookie.split(';');
        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }

    // Cookie-Einstellungen aus dem Cookie laden oder Standardwerte setzen
    function getCookieSettings() {
        const settings = getCookie('cookie-settings');
        if (settings) {
            try {
                return JSON.parse(settings);
            } catch (e) {
                console.error('<PERSON><PERSON> beim <PERSON> <PERSON>ie-Einstellungen:', e);
            }
        }
        return {
            essential: true, // Immer aktiviert
            recaptcha: false // Standardmäßig deaktiviert
        };
    }

    // Cookie-Einstellungen speichern
    function saveCookieSettings(settings) {
        setCookie('cookie-settings', JSON.stringify(settings), 365);
        setCookie('cookie-consent', settings.recaptcha ? 'accepted' : 'rejected', 365);
        applyRecaptchaSettings(settings.recaptcha);
    }

    // reCAPTCHA ein- oder ausblenden basierend auf den Einstellungen
    function applyRecaptchaSettings(enabled) {
        const recaptchaElements = document.querySelectorAll('.g-recaptcha');
        
        if (enabled) {
            // reCAPTCHA aktivieren
            recaptchaElements.forEach(element => {
                element.style.display = 'block';
            });
            
            // reCAPTCHA-Script laden, falls noch nicht geladen
            if (typeof grecaptcha === 'undefined') {
                const script = document.createElement('script');
                script.src = 'https://www.google.com/recaptcha/api.js';
                script.async = true;
                script.defer = true;
                document.head.appendChild(script);
            }
        } else {
            // reCAPTCHA deaktivieren
            recaptchaElements.forEach(element => {
                element.style.display = 'none';
            });
        }
    }

    // Cookie-Banner anzeigen, wenn noch keine Entscheidung getroffen wurde
    const cookieConsent = getCookie('cookie-consent');
    const cookieBanner = document.getElementById('cookie-banner');
    const cookieSettingsModal = document.getElementById('cookie-settings-modal');
    
    // Elemente für die Cookie-Einstellungen
    const recaptchaCookiesCheckbox = document.getElementById('recaptcha-cookies');
    const cookieSettingsButton = document.getElementById('cookie-settings');
    const saveCookieSettingsButton = document.getElementById('save-cookie-settings');
    const closeCookieSettingsButton = document.getElementById('close-cookie-settings');
    const footerCookieSettingsButton = document.getElementById('footer-cookie-settings');
    
    // Aktuelle Einstellungen laden und anwenden
    const currentSettings = getCookieSettings();
    if (recaptchaCookiesCheckbox) {
        recaptchaCookiesCheckbox.checked = currentSettings.recaptcha;
    }
    
    // reCAPTCHA-Einstellungen anwenden
    applyRecaptchaSettings(currentSettings.recaptcha);
    
    // Cookie-Banner anzeigen, wenn noch keine Entscheidung getroffen wurde
    if (!cookieConsent && cookieBanner) {
        cookieBanner.style.display = 'block';
    }

    // Event-Listener für die Buttons
    const acceptButton = document.getElementById('accept-cookies');
    const rejectButton = document.getElementById('reject-cookies');

    if (acceptButton) {
        acceptButton.addEventListener('click', function() {
            const settings = {
                essential: true,
                recaptcha: true
            };
            saveCookieSettings(settings);
            cookieBanner.style.display = 'none';
        });
    }

    if (rejectButton) {
        rejectButton.addEventListener('click', function() {
            const settings = {
                essential: true,
                recaptcha: false
            };
            saveCookieSettings(settings);
            cookieBanner.style.display = 'none';
        });
    }
    
    // Cookie-Einstellungen öffnen (vom Banner)
    if (cookieSettingsButton && cookieSettingsModal) {
        cookieSettingsButton.addEventListener('click', function() {
            cookieSettingsModal.classList.remove('hidden');
        });
    }
    
    // Cookie-Einstellungen öffnen (vom Footer)
    if (footerCookieSettingsButton && cookieSettingsModal) {
        footerCookieSettingsButton.addEventListener('click', function(e) {
            e.preventDefault();
            cookieSettingsModal.classList.remove('hidden');
        });
    }
    
    // Cookie-Einstellungen schließen
    if (closeCookieSettingsButton && cookieSettingsModal) {
        closeCookieSettingsButton.addEventListener('click', function() {
            cookieSettingsModal.classList.add('hidden');
        });
    }
    
    // Cookie-Einstellungen speichern
    if (saveCookieSettingsButton && cookieSettingsModal) {
        saveCookieSettingsButton.addEventListener('click', function() {
            const settings = {
                essential: true, // Immer aktiviert
                recaptcha: recaptchaCookiesCheckbox ? recaptchaCookiesCheckbox.checked : false
            };
            saveCookieSettings(settings);
            cookieSettingsModal.classList.add('hidden');
            cookieBanner.style.display = 'none';
            
            // Wenn reCAPTCHA aktiviert wurde, aber Formulare auf der Seite sind,
            // zeige einen Hinweis, dass die Seite neu geladen werden muss
            if (settings.recaptcha && document.querySelectorAll('form').length > 0) {
                // Optional: Hinweis anzeigen, dass die Seite neu geladen werden sollte
                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        title: 'Cookie-Einstellungen gespeichert',
                        text: 'Für die vollständige Funktionalität von reCAPTCHA empfehlen wir, die Seite neu zu laden.',
                        icon: 'info',
                        confirmButtonText: 'Seite neu laden',
                        showCancelButton: true,
                        cancelButtonText: 'Später'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.reload();
                        }
                    });
                }
            }
        });
    }
    
    // Klick außerhalb des Modals schließt es
    if (cookieSettingsModal) {
        cookieSettingsModal.addEventListener('click', function(e) {
            if (e.target === cookieSettingsModal) {
                cookieSettingsModal.classList.add('hidden');
            }
        });
    }
    
    // Hinweis anzeigen, wenn reCAPTCHA deaktiviert ist und ein Formular abgesendet wird
    document.querySelectorAll('form').forEach(form => {
        if (form.querySelector('.g-recaptcha')) {
            form.addEventListener('submit', function(e) {
                const settings = getCookieSettings();
                if (!settings.recaptcha) {
                    e.preventDefault();
                    
                    // SweetAlert verwenden, wenn verfügbar
                    if (typeof Swal !== 'undefined') {
                        Swal.fire({
                            title: 'reCAPTCHA deaktiviert',
                            text: 'Um das Formular abzusenden, müssen Sie reCAPTCHA in den Cookie-Einstellungen aktivieren.',
                            icon: 'warning',
                            confirmButtonText: 'Cookie-Einstellungen öffnen',
                            showCancelButton: true,
                            cancelButtonText: 'Abbrechen'
                        }).then((result) => {
                            if (result.isConfirmed && cookieSettingsModal) {
                                cookieSettingsModal.classList.remove('hidden');
                            }
                        });
                    } else {
                        // Fallback für den Fall, dass SweetAlert nicht verfügbar ist
                        alert('Um das Formular abzusenden, müssen Sie reCAPTCHA in den Cookie-Einstellungen aktivieren.');
                        if (cookieSettingsModal) {
                            cookieSettingsModal.classList.remove('hidden');
                        }
                    }
                }
            });
        }
    });
});

