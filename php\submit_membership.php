<?php
// php/submit_membership.php
header('Content-Type: application/json');

require_once __DIR__ . '/config.php'; // Config laden
require_once BASE_PATH . '/php/db_connect.php'; // DB Connect mit BASE_PATH
require_once BASE_PATH . '/php/recaptcha_verify.php'; // reCAPTCHA mit BASE_PATH

$response = ['success' => false];

// 1. Nur POST-Requests erlauben
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['error_code'] = 'invalid_method';
    echo json_encode($response);
    exit;
}

// 2. reCAPTCHA überprüfen
$recaptchaToken = $_POST['g-recaptcha-response'] ?? '';
if (!verifyRecaptcha($recaptchaToken)) {
    $response['error_code'] = 'recaptcha_failed';
    echo json_encode($response);
    exit;
}

// 3. Daten aus POST holen und Basis-Sanitization
$titel = trim($_POST['titel'] ?? '');
$anrede = trim($_POST['anrede'] ?? '');
$vorname = trim($_POST['vorname'] ?? '');
$nachname = trim($_POST['nachname'] ?? '');
$organisation = trim($_POST['organisation'] ?? '') ?: null;
$strasse_hausnummer = trim($_POST['strasse_hausnummer'] ?? '');
$plz = trim($_POST['plz'] ?? '');
$ort = trim($_POST['ort'] ?? '');
$land = trim($_POST['land'] ?? '');
$geburtsdatum = trim($_POST['geburtsdatum'] ?? '');
$telefon = trim($_POST['telefon'] ?? ''); // Ist jetzt Pflicht
$email = filter_var(trim($_POST['email'] ?? ''), FILTER_SANITIZE_EMAIL);
$gewuenschtes_zahlungsintervall = trim($_POST['gewuenschtes_zahlungsintervall'] ?? '');
$beitrag_input = trim($_POST['beitrag'] ?? ''); // Beitrag (optional)
$ist_student = isset($_POST['ist_student']) && $_POST['ist_student'] == '1';
$datenschutz_akzeptiert = isset($_POST['datenschutz_akzeptiert']);
$bestaetigung_richtigkeit = isset($_POST['bestaetigung_richtigkeit']);
$keine_andere_partei = isset($_POST['keine_andere_partei']);
$art_der_mitgliedschaft = 'Fördermitglied';

// Geschlecht aus Anrede ableiten
$geschlecht = 'o'; // Default: other/divers
if ($anrede === 'Herr') {
    $geschlecht = 'm';
} elseif ($anrede === 'Frau') {
    $geschlecht = 'w';
}

// --- Serverseitige Validierung ---
$errors = [];
if (empty($anrede)) $errors[] = 'Anrede fehlt.';
if (empty($vorname)) $errors[] = 'Vorname fehlt.';
if (empty($nachname)) $errors[] = 'Nachname fehlt.';
if (empty($strasse_hausnummer)) $errors[] = 'Straße und Hausnummer fehlen.';
if (empty($plz) || !preg_match('/^[0-9]{5}$/', $plz)) $errors[] = 'PLZ ungültig.';
if (empty($ort)) $errors[] = 'Ort fehlt.';
if (empty($land)) $errors[] = 'Land fehlt.';
if (empty($geburtsdatum) || !($dateObj = DateTime::createFromFormat('Y-m-d', $geburtsdatum)) || $dateObj->format('Y-m-d') !== $geburtsdatum) $errors[] = 'Geburtsdatum ungültig.';
if (empty($telefon)) $errors[] = 'Telefonnummer fehlt.';
if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = 'E-Mail ungültig.';
if (!in_array($gewuenschtes_zahlungsintervall, ['monatlich', 'vierteljährlich', 'halbjährlich', 'jährlich'])) $errors[] = 'Zahlungsintervall ungültig.';

// === BEITRAG VALIDIERUNG/STANDARDWERT (Bleibt gleich) ===
$beitrag_db = '2.50'; // Standardwert setzen
if ($beitrag_input !== '') { // Nur validieren, wenn etwas eingegeben wurde
    $beitrag_numeric = str_replace(',', '.', $beitrag_input);
    if (!is_numeric($beitrag_numeric)) {
        $errors[] = 'Beitrag muss eine Zahl sein.';
        $beitrag_db = '2.50'; // Bei Fehler auf Standard zurückfallen
    } elseif ((float)$beitrag_numeric < 2.50) {
        $errors[] = 'Der Mindestbeitrag beträgt 2,50 €.';
        $beitrag_db = '2.50'; // Bei Fehler auf Standard zurückfallen
    } else {
        // Nur wenn gültig, den eingegebenen Wert nehmen
        $beitrag_db = number_format((float)$beitrag_numeric, 2, '.', '');
    }
}
// $beitrag_db enthält jetzt entweder '2.50' oder den validierten User-Input

// Checkbox-Validierung
if (!$bestaetigung_richtigkeit) $errors[] = 'Bestätigung der Richtigkeit fehlt.';
if (!$datenschutz_akzeptiert) $errors[] = 'Datenschutzzustimmung fehlt.';
if (!$keine_andere_partei) $errors[] = 'Bestätigung "Keine andere Partei" fehlt.';


if (!empty($errors)) {
    $response['error_code'] = 'validation_failed';
    $response['errors'] = $errors;
    echo json_encode($response);
    exit;
}

// 4. Prüfen, ob NAMENSKOMBINATION bereits existiert (unverändert)
$duplicateCheckError = null;
try {
    $lowerVorname = mb_strtolower($vorname, 'UTF-8');
    $lowerNachname = mb_strtolower($nachname, 'UTF-8');
    $duplicateFound = false;

    // 4.1: Prüfe 'antraege'
    $sqlCheckAntraege = "SELECT 1 FROM antraege WHERE (LOWER(vorname) = :lv1 AND LOWER(nachname) = :ln1) OR (LOWER(vorname) = :ln2 AND LOWER(nachname) = :lv2) LIMIT 1";
    $stmtCheckAntraege = $pdo->prepare($sqlCheckAntraege);
    $stmtCheckAntraege->bindParam(':lv1', $lowerVorname, PDO::PARAM_STR);
    $stmtCheckAntraege->bindParam(':ln1', $lowerNachname, PDO::PARAM_STR);
    $stmtCheckAntraege->bindParam(':lv2', $lowerVorname, PDO::PARAM_STR);
    $stmtCheckAntraege->bindParam(':ln2', $lowerNachname, PDO::PARAM_STR);
    $stmtCheckAntraege->execute();
    if ($stmtCheckAntraege->fetchColumn()) {
        $duplicateFound = true;
    }

    // 4.2: Prüfe 'mitglieder'
    if (!$duplicateFound) {
        $sqlCheckMitglieder = "SELECT 1 FROM mitglieder WHERE (LOWER(vorname) = :lv1 AND LOWER(name) = :ln1) OR (LOWER(vorname) = :ln2 AND LOWER(name) = :lv2) LIMIT 1";
        $stmtCheckMitglieder = $pdo->prepare($sqlCheckMitglieder);
        $stmtCheckMitglieder->bindParam(':lv1', $lowerVorname, PDO::PARAM_STR);
        $stmtCheckMitglieder->bindParam(':ln1', $lowerNachname, PDO::PARAM_STR);
        $stmtCheckMitglieder->bindParam(':lv2', $lowerVorname, PDO::PARAM_STR);
        $stmtCheckMitglieder->bindParam(':ln2', $lowerNachname, PDO::PARAM_STR);
        $stmtCheckMitglieder->execute();
        if ($stmtCheckMitglieder->fetchColumn()) {
            $duplicateFound = true;
        }
    }

    // 4.3: Fehler senden, wenn Duplikat gefunden
    if ($duplicateFound) {
        $response['error_code'] = 'name_exists';
        echo json_encode($response);
        exit;
    }

} catch (PDOException $e) {
    $duplicateCheckError = $e->getMessage();
    error_log("DB Duplicate Check PDOException: " . $duplicateCheckError);
}

// 5. Daten in die 'antraege' Tabelle einfügen (NUR wenn kein DB-Fehler bei der Prüfung auftrat)
if ($duplicateCheckError === null) {
    $sql = "INSERT INTO antraege (
                titel, anrede, vorname, nachname, organisation, geschlecht, strasse, ort, plz, land,
                geburtsdatum, telefon, email, gewuenschtes_zahlungsintervall, ist_student,
                datenschutz_akzeptiert, art_der_mitgliedschaft, beitrag,
                bestaetigung_richtigkeit, keine_andere_partei,
                antrag_status, eingegangen_am
            ) VALUES (
                :titel, :anrede, :vorname, :nachname, :organisation, :geschlecht, :strasse_hausnummer, :ort, :plz, :land,
                :geburtsdatum, :telefon, :email, :gewuenschtes_zahlungsintervall, :ist_student,
                :datenschutz_akzeptiert, :art_der_mitgliedschaft, :beitrag,
                :bestaetigung_richtigkeit, :keine_andere_partei,
                'Eingegangen', NOW()
            )";

    try {
        $stmt = $pdo->prepare($sql);

        // Binden der Parameter
        $stmt->bindParam(':titel', $titel);
        $stmt->bindParam(':anrede', $anrede);
        $stmt->bindParam(':vorname', $vorname);
        $stmt->bindParam(':nachname', $nachname);
        $stmt->bindParam(':organisation', $organisation, PDO::PARAM_STR|PDO::PARAM_NULL);
        $stmt->bindParam(':geschlecht', $geschlecht);
        $stmt->bindParam(':strasse_hausnummer', $strasse_hausnummer);
        $stmt->bindParam(':plz', $plz);
        $stmt->bindParam(':ort', $ort);
        $stmt->bindParam(':land', $land);
        $stmt->bindParam(':geburtsdatum', $geburtsdatum);
        $stmt->bindParam(':telefon', $telefon, PDO::PARAM_STR);
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':gewuenschtes_zahlungsintervall', $gewuenschtes_zahlungsintervall);
        $stmt->bindParam(':ist_student', $ist_student, PDO::PARAM_BOOL);
        $stmt->bindParam(':datenschutz_akzeptiert', $datenschutz_akzeptiert, PDO::PARAM_BOOL);
        $stmt->bindParam(':art_der_mitgliedschaft', $art_der_mitgliedschaft);
        $stmt->bindParam(':beitrag', $beitrag_db); // Verwendet $beitrag_db (Standard oder validierter Wert)
        $stmt->bindParam(':bestaetigung_richtigkeit', $bestaetigung_richtigkeit, PDO::PARAM_BOOL);
        $stmt->bindParam(':keine_andere_partei', $keine_andere_partei, PDO::PARAM_BOOL);

        if ($stmt->execute()) {
            // Antrag-ID abrufen
            $antrag_id = $pdo->lastInsertId();
            
            // Antragsnummer generieren (ANT-XXXXX)
            $antragsnummer = 'ANT-' . str_pad($antrag_id, 5, '0', STR_PAD_LEFT);
            
            // Antragsnummer aktualisieren
            $updateSql = "UPDATE antraege SET antragsnummer = :antragsnummer WHERE antrag_id = :antrag_id";
            $updateStmt = $pdo->prepare($updateSql);
            $updateStmt->bindParam(':antragsnummer', $antragsnummer);
            $updateStmt->bindParam(':antrag_id', $antrag_id);
            $updateStmt->execute();
            
            $response['success'] = true;
        } else {
            $response['error_code'] = 'db_insert_failed';
            $errorInfo = $stmt->errorInfo();
            error_log("DB Insert Error: Statement execution failed. Info: " . json_encode($errorInfo));
        }

    } catch (PDOException $e) {
        error_log("DB Insert PDO Error: " . $e->getMessage());
        $response['error_code'] = 'db_error';
    }
} else {
    $response['error_code'] = 'db_error';
    $response['message'] = 'Fehler bei der Datenbankprüfung auf Duplikate.';
}

// 6. JSON-Antwort senden
echo json_encode($response);
?>
