<?php
// Verfügbare Sprachen
$available_languages = ['de', 'en', 'ar', 'tr'];

// Standardsprache
$default_language = 'de';

// Sprache aus URL-Parameter oder Session oder Cookie lesen
function get_user_language() {
    global $available_languages, $default_language;
    
    // 1. <PERSON><PERSON><PERSON><PERSON>, ob ein lang-Parameter in der URL ist
    if (isset($_GET['lang']) && in_array($_GET['lang'], $available_languages)) {
        $lang = $_GET['lang'];
        
        // Sprache in Session und Cookie speichern
        $_SESSION['user_language'] = $lang;
        setcookie('user_language', $lang, time() + (86400 * 30), "/"); // 30 Tage gültig
        
        return $lang;
    }
    
    // 2. Pr<PERSON><PERSON>, ob Sprache in der Session gespeichert ist
    if (isset($_SESSION['user_language']) && in_array($_SESSION['user_language'], $available_languages)) {
        return $_SESSION['user_language'];
    }
    
    // 3. <PERSON><PERSON><PERSON><PERSON>, ob Sprache im Cookie gespeichert ist
    if (isset($_COOKIE['user_language']) && in_array($_COOKIE['user_language'], $available_languages)) {
        return $_COOKIE['user_language'];
    }
    
    // 4. Fallback auf Standardsprache
    return $default_language;
}

// Aktuelle Sprache ermitteln
$current_language = get_user_language();

// JavaScript-Variable für die aktuelle Sprache setzen
$language_script = "<script>const serverLanguage = '{$current_language}';</script>";
?>