"use strict";class FloatMenu{static ATTR="data-float-menu";static initialize(){window.floatMenus={},document.querySelectorAll(`[${FloatMenu.ATTR}]`).forEach((e=>{const t=new FloatMenu(e),s=e.className.match(/float-menu-(\d+)/);if(s&&s[1]){const e=s[1];window.floatMenus[e]=t}}))}constructor(e,t={}){if(!(e instanceof HTMLElement))return;if(this.element=e,this.config=Object.assign({},{position:["left","center"]},this.#e(),t),null===this.config)return;if(this.items=e.querySelectorAll(".fm-item"),this.links=e.querySelectorAll(".fm-link"),this.labels=e.querySelectorAll(".fm-label"),0===this.items.length)return;this.itemWidth=this.items[0].offsetWidth+15,this.config?.remove&&this.element.removeAttribute(FloatMenu.ATTR),this.init()}init(){this.screen(),window.addEventListener("resize",this.screen.bind(this)),this.mobileStyle(),window.addEventListener("resize",this.mobileStyle.bind(this)),this.labelDisabele(),this.mobileClick(),this.position(),this.appearance(),this.setLinkProperties(),this.setSubMenu(),this.extraText(),this.visibleMenu(),this.closePopup()}appearance(){this.config?.appearance&&(this.config?.appearance?.shape&&this.element.classList.add(this.config?.appearance?.shape),this.config?.appearance?.sideSpace&&this.element.classList.add("-side-space"),this.config?.appearance?.buttonSpace&&this.element.classList.add("-button-space"),this.config?.appearance?.labelConnected&&this.element.classList.add("-label-connect"),this.config?.appearance?.subSpace&&this.element.classList.add("-sub-space"))}labelDisabele(){if(!this.config?.label?.off)return!1;this.links.forEach((e=>{e.classList.add("-label-hidden")}))}screen(){if(!this.config?.screen)return;const{small:e,large:t}=this.config.screen,s=window.innerWidth,i=()=>{this.element.classList.add("fm-hidden")},n=()=>{this.element.classList.remove("fm-hidden")};void 0!==e&&s<=e||void 0!==t&&s>=t?i():(void 0===e||void 0===t||s>e&&s<t)&&n()}closePopup(){const e=this.element.querySelectorAll(".fm-window");0!==e.length&&e.forEach((e=>{const t=e.querySelector(".fm-close");t&&t.addEventListener("click",(()=>{e.close()})),e.addEventListener("click",(function({currentTarget:e,target:t}){const s=e;t===s&&s.close()}))}))}extraText(){const e=this.element.querySelectorAll(".fm-extra-text");0!==e.length&&e.forEach((e=>{const t=parseFloat(this.config?.label?.space)||0;e.style.setProperty("--text_margin",t);const s=e.closest(".fm-item"),i=s.querySelector(".fm-link");s.addEventListener("mouseenter",(()=>{i.classList.toggle("-active")})),s.addEventListener("mouseleave",(()=>{i.classList.toggle("-active")}))}))}visibleMenu(){if((!this.config.visible||this.config.visible.every((e=>"show"===e||"0"===e)))&&(!this.config.time||this.config.time.every((e=>"show"===e||"0"===e))))return void this.element.classList.add("fm-ready");let e=!1;const t=()=>{e||(this.element.classList.add("fm-ready"),e=!0)},s=()=>{e&&(this.element.classList.remove("fm-ready"),e=!1)};if(this.config.time){const e=this.config.time||["show","0"],[i,n]=e,l=Number(n);"show"===i&&l>0&&setTimeout(t,1e3*l),"hide"===i&&l>0&&(t(),setTimeout(s,1e3*l))}if(this.config.visible){const e=this.config.visible||["show","0"],[i,n]=e,l=Number(n);"hide"===i&&t(),window.addEventListener("scroll",(()=>{const e=window.scrollY||document.documentElement.scrollTop;"show"===i&&(e>=l?t():s()),"hide"===i&&(e>l?s():t())}))}}mobileStyle(){if(!this.config?.mobile)return;const e=window.innerWidth,t=parseInt(this.config?.mobile[0])||0,s=parseInt(this.config?.mobile[1])||24,i=parseInt(this.config?.mobile[2])||15;e<t&&(this.element.style.setProperty("--fm-icon-size",s),this.element.style.setProperty("--fm-label-size",i))}mobileClick(){this._isMobile()&&this.config?.mobileRules&&(this.links.forEach((e=>{e.addEventListener("click",(t=>{e.classList.contains("-active")||(t.preventDefault(),t.stopImmediatePropagation(),this.links.forEach((e=>{e.classList.remove("-active")})),e.classList.add("-active"),clearTimeout(undefined),setTimeout((()=>{e.classList.remove("-active"),e.blur()}),3e3))}))})),document.addEventListener("click",(e=>{this.element.contains(e.target)||this.links.forEach((e=>{const t=e.closest(".fm-item");e.classList.remove("-active"),t.classList.remove("-active")}))})))}position(){let e=this.config?.position[0]||"left",t=this.config?.position[1]||"center";this.element.classList.add(`-${e}`),this.element.classList.add(`-${t}`);const s=this.element.getBoundingClientRect();let i=0,n=0;this.config?.offset&&(i=parseInt(this.config?.offset[0]),n=parseInt(this.config?.offset[1])),0!==i&&this.element.style.setProperty("--fm-offset",`${i}px`);let l=s.top+n;if("center"!==t)this.element.style.top=`${l}px`;else{l-=this.element.offsetHeight/2,this.element.style.top=`${l}px`}}setLinkProperties(){const e=parseFloat(this.config?.label?.space)||0,t=this.config?.label?.effect||"none";this.links.forEach((s=>{const i=s.querySelector(".fm-label").offsetWidth+e;s.offsetWidth;s.style.setProperty("--_width",i),s.classList.add(`-${t}`)}))}setSubMenu(){if(!this.config?.sub)return;const e=this.element.querySelectorAll(".fm-has-sub");if(0===e.length)return;const t=this.config?.sub?.position||"under",s=this.config?.sub?.effect||"none",i=this.config?.sub?.open||"click";let n=null;e.forEach((e=>{e.classList.add(`fm-sub-${t}`),e.classList.add(`-sub-${s}`);const l=[];let o=e.previousElementSibling;for(;o;)o.classList.contains("fm-item")&&l.push(o),o=o.previousElementSibling;this.setSubProperties(e,s,t),"click"===i||this._isMobile()?e.addEventListener("click",(s=>{s.stopPropagation();const i=e.querySelector(".fm-link");n&&n!==e&&this.closeSubMenu(n,t),n&&n.contains(s.target)&&(s.target.matches(".fm-sub")||s.target.closest(".fm-link"))&&i&&!i.contains(s.target)||(e.classList.toggle("-active"),i.classList.toggle("-active"),n=e.classList.contains("-active")?e:null,"circular"===t&&(i.classList.toggle("-label-hidden"),l.length>0&&l.forEach((e=>{e.classList.toggle("-hidden")}))))})):(e.addEventListener("mouseenter",(()=>{const s=e.querySelector(".fm-link");e.classList.add("-active"),s.classList.add("-active"),"circular"===t&&(s.classList.toggle("-label-hidden"),l.length>0&&l.forEach((e=>{e.classList.toggle("-hidden")})))})),e.addEventListener("mouseleave",(()=>{this.closeSubMenu(e,t)})))})),document.addEventListener("click",(e=>{n&&!n.contains(e.target)&&(this.closeSubMenu(n,t),n=null)}))}closeSubMenu(e,t){const s=e.querySelector(".fm-link");if(e.classList.remove("-active"),s.classList.remove("-active"),"circular"===t){s.classList.toggle("-label-hidden");const e=this.element.querySelectorAll(".fm-item.-hidden");e.length>0&&e.forEach((e=>{e.classList.remove("-hidden")}))}}setSubProperties(e,t,s){const i=e.querySelector(".fm-sub"),n=i.offsetHeight;"circular"!==s&&e.style.setProperty("--_offset",n);const l=i.querySelectorAll(".fm-item"),o=parseInt(this.config?.sub?.speed)||0,c=l.length,a=o/c;let r=l[0].offsetWidth;"linear-fade"===t&&l.length>0&&l.forEach(((e,t)=>{const s=t*a,i=(c-1-t)*a;e.style.setProperty("--_delay",`${s}`),e.style.setProperty("--_close_delay",`${i}`)})),"linear-slide"===t&&l.length>0&&l.forEach(((e,t)=>{const s=(t+1)*r*-1;e.style.setProperty("--_top",`${s}px`)})),"circular"===s&&this.subMenuCircular(i,l,e)}subMenuCircular(e,t,s){const i=this.itemWidth,n=this.calculateAngleIncrement(t.length);let l=this.calculateOptimalRadius(i,n);const o=1.5*i;l<o&&(l=o),s.style.setProperty("--_offset",l),s.style.setProperty("--_box",l),this.setMenuProperties(t,n,l,e,i)}setMenuProperties(e,t,s,i,n){const l=s+2;i.classList.contains("-active")?i.style.setProperty("margin-bottom",`${l}px`):i.style.setProperty("margin-bottom","unset");const o=(parseInt(this.config?.sub?.speed)||0)/e.length;e.forEach(((n,l)=>{const c=e.length-1-l,a=this.determineAngleForMenu(i,t,l,c);this.styleMenuItem(n,i,s,a,l,o)}))}determineAngleForMenu(e,t,s,i){const n=this.config?.position[0]||"left";return"left"===n?t*s-Math.PI/2:"right"===n?t*i+Math.PI/2:void 0}styleMenuItem(e,t,s,i,n,l){const o=n*l,c=s*Math.cos(i),a=s*Math.sin(i);e.style.setProperty("--x",`${c}px`),e.style.setProperty("--y",`${a}px`),e.style.setProperty("--_delay",`${o}`)}calculateAngleIncrement(e){return Math.PI/(e-1)}calculateOptimalRadius(e,t){return e/2/Math.sin(t/2)}#e(){const e=this.element.getAttribute(`${FloatMenu.ATTR}`);if(!e||""===e.trim())return{};try{return JSON.parse(e)}catch(e){return{}}}_isMobile(){return/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)||window.innerWidth<=768||"ontouchstart"in window||navigator.maxTouchPoints>0}_isObjEmpty(e){return e&&"object"==typeof e&&0===Object.keys(e).length}_showConfig(){console.log(this.config)}}document.addEventListener("DOMContentLoaded",(function(){FloatMenu.initialize()}));